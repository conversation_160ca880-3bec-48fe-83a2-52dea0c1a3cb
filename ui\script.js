// 彩票店UI脚本

class LotteryUI {
    constructor() {
        this.isOpen = false;
        this.currentShop = null;
        this.playerData = null;
        this.config = null;
        this.selectedNumbers = {
            red: [],
            blue: null,
            front: [],
            back: [],
            arrangeFive: '00000'
        };
        this.scratchData = null;
        this.scratchCanvas = null;
        this.scratchContext = null;
        this.isScratching = false;
        this.scratchCheckTimeout = null; // 添加刮奖检查的防抖定时器
        this.resourcesLoaded = false;
        this.autoClaimSent = false;
        this.isAnnouncementOpen = false; // 添加跟踪中奖公告显示状态的变量
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupScratchCanvas();
        
        // 监听NUI消息
        window.addEventListener('message', (event) => {
            const data = event.data;
            
            if (!data.action) return;
            
            switch (data.action) {
                case 'openLottery':
                    this.openLottery(data);
                    break;
                case 'closeLottery':
                    this.closeLottery();
                    break;
                case 'prepareResources':
                    this.prepareResources(data.cardType);
                    break;
                case 'openScratchCard':
                    this.openScratchCard(data);
                    break;
                case 'scratchResult':
                    this.showScratchResult(data.result);
                    break;
                case 'drawResult':
                    this.showDrawResult(data.drawData);
                    break;
                case 'showAnnouncement':
                    this.showAnnouncement(data.message, data.type);
                    break;
                case 'showNotification':
                    this.showMessage(data.message, data.type, data.title);
                    break;
                case 'updatePrizePools':
                    this.updatePrizePools(data.pools);
                    break;
                case 'claimPrizeResult':
                    if (data.result.success === true) {
                        // 构建成功消息，包含手续费信息
                        let successMessage = '兑奖成功！';
                        
                        // 如果有手续费，显示手续费信息
                        if (data.result.feeAmount > 0) {
                            successMessage = `兑奖成功！原始奖金：${this.formatNumber(data.result.originalAmount)}，`;
                            successMessage += `手续费：${this.formatNumber(data.result.feeAmount)}`;
                            
                            // 如果有手续费提示信息，显示在弹窗中
                            if (data.result.feeMessage) {
                                // 使用公告模式显示详细信息
                                this.showAnnouncement(
                                    `<div class="fee-info">
                                        <div class="fee-title">兑奖成功</div>
                                        <div class="fee-details">
                                            <p>${data.result.feeMessage}</p>
                                            <p>原始奖金: <span class="highlight">${this.formatNumber(data.result.originalAmount)}</span></p>
                                            <p>手续费: <span class="highlight">${this.formatNumber(data.result.feeAmount)}</span></p>
                                            <p>实际获得: <span class="highlight">${this.formatNumber(data.result.amount)}</span></p>
                                        </div>
                                    </div>`,
                                    'success'
                                );
                                
                                // 已经使用公告显示了，不再使用普通消息
                                successMessage = null;
                            }
                        }
                        
                        // 如果有消息，显示成功消息
                        if (successMessage) {
                            this.showMessage(successMessage, 'success');
                        }
                        
                        // 立即从界面上移除这个记录
                        const claimedTicketId = data.result.ticketId;
                        if (claimedTicketId) {
                            const prizeItem = document.querySelector(`.prize-item[data-ticket-id="${claimedTicketId}"]`);
                            if (prizeItem) {
                                // 添加淡出动画效果
                                prizeItem.style.transition = 'opacity 0.3s ease-out';
                                prizeItem.style.opacity = '0';
                                setTimeout(() => {
                                    if (prizeItem.parentNode) {
                                        prizeItem.remove();
                                    }
                                }, 300);
                            }
                        }
                        // 立即重新加载未兑奖列表和相关数据
                        this.loadUnclaimedPrizes();
                        this.loadPrizePools(); // 刷新奖池数据

                        // 刷新历史记录，显示最新状态
                        const activeHistoryTab = document.querySelector('.history-nav-btn.active');
                        if (activeHistoryTab) {
                            this.switchHistoryTab(activeHistoryTab.dataset.history);
                        }

                        // 延迟再次刷新，确保数据库更新完全完成
                        setTimeout(() => {
                            this.loadUnclaimedPrizes();
                        }, 200);
                    } else {
                        this.showMessage(data.result.message || '兑奖失败', 'error');
                    }
                    this.showLoading(false);
                    break;
                case 'updateUnclaimedPrizes':
                    this.updateUnclaimedPrizes(data.prizes);
                    break;
                case 'buyResult':
                    this.handleBuyResult(data.result);
                    break;
                case 'receiveDrawHistory':
                    this.handleDrawHistoryData(data.data);
                    break;
                case 'updatePlayerTickets':
                    // 首先尝试查找当前活动的历史类型
                    const activeHistoryBtn = document.querySelector('.history-nav-btn.active');
                    let historyType = activeHistoryBtn ? activeHistoryBtn.dataset.history : null;
                    
                    // 查找等待数据的容器
                    const waitingContainer = document.querySelector('[data-waiting-data="true"]');
                    if (waitingContainer) {
                        historyType = waitingContainer.dataset.historyType;
                        
                        // 移除等待标记
                        waitingContainer.removeAttribute('data-waiting-data');
                    }
                    
                    // 如果找到了历史类型，渲染数据
                    if (historyType) {
                        const listElement = historyType === 'scratch' ? 
                            'scratch-history-list' : 'lottery-history-list';
                            
                        this.renderHistoryList(data.tickets, listElement, historyType);
                    }
                    break;
                case 'updateScratchStats':
                    this.renderScratchStats(data.stats);
                    break;
                case 'checkAnnouncementStatus':
                    // 由于不再使用模态弹窗，直接返回false，允许ESC键关闭界面
                    fetch(`https://${this.GetParentResourceName()}/announcementStatus`, {
                        method: 'POST',
                        body: JSON.stringify({ isAnnouncementOpen: false })
                    });
                    break;
            }
        });
    }
    
    bindEvents() {
        // 关闭按钮
        document.getElementById('close-lottery').addEventListener('click', () => {
            this.closeLottery();
        });
        
        document.getElementById('close-scratch').addEventListener('click', () => {
            this.closeScratchCard();
        });
        
        // 导航标签
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.switchTab(btn.dataset.tab);
            });
        });
        
        // 历史记录导航
        document.querySelectorAll('.history-nav-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.switchHistoryTab(btn.dataset.history);
            });
        });
        
        // 刮刮乐购买
        document.querySelectorAll('.card-item').forEach(card => {
            card.addEventListener('click', () => {
                this.buyScratchCard(card.dataset.card);
            });
        });
        
        // 双色球操作
        document.getElementById('random-double-ball').addEventListener('click', () => {
            this.randomDoubleBall();
        });
        
        document.getElementById('clear-double-ball').addEventListener('click', () => {
            this.clearDoubleBall();
        });
        
        document.getElementById('buy-double-ball').addEventListener('click', () => {
            this.buyDoubleBall();
        });
        
        // 大乐透操作
        document.getElementById('random-super-lotto').addEventListener('click', () => {
            this.randomSuperLotto();
        });
        
        document.getElementById('clear-super-lotto').addEventListener('click', () => {
            this.clearSuperLotto();
        });
        
        document.getElementById('buy-super-lotto').addEventListener('click', () => {
            this.buySuperLotto();
        });

        // 排列5操作
        document.getElementById('arrange-five-number').addEventListener('input', (e) => {
            this.updateArrangeFiveNumber(e.target.value);
        });

        document.getElementById('random-arrange-five').addEventListener('click', () => {
            this.randomArrangeFive();
        });

        document.getElementById('clear-arrange-five').addEventListener('click', () => {
            this.clearArrangeFive();
        });

        document.getElementById('buy-arrange-five').addEventListener('click', () => {
            this.buyArrangeFive();
        });

        // 刮刮乐操作
        document.getElementById('reveal-all').addEventListener('click', () => {
            this.revealAll();
        });
        
        document.getElementById('finish-scratch').addEventListener('click', () => {
            this.finishScratch();
        });
        
        // 模态框关闭
        // 移除这里的事件绑定，改为在showAnnouncement中动态绑定
        // document.getElementById('close-announcement').addEventListener('click', () => {
        //     document.getElementById('announcement-modal').classList.add('hidden');
        //     // 关闭弹窗时重置焦点，使鼠标不可见
        //     fetch(`https://${this.GetParentResourceName()}/focus`, {
        //         method: 'POST',
        //         body: JSON.stringify({ focus: false })
        //     });
        // });
        
        document.getElementById('confirm-cancel').addEventListener('click', () => {
            document.getElementById('confirm-modal').classList.add('hidden');
        });
        
        document.getElementById('confirm-ok').addEventListener('click', () => {
            if (this.confirmCallback) {
                this.confirmCallback();
            }
            document.getElementById('confirm-modal').classList.add('hidden');
        });
        
        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // 如果中奖公告显示中，则不关闭任何界面
                if (this.isAnnouncementOpen) {
                    // 阻止默认行为，确保ESC键不会关闭任何界面
                    e.preventDefault();
                    return;
                }
                
                // 正常情况下的关闭逻辑
                if (!document.getElementById('scratch-game').classList.contains('hidden')) {
                    this.closeScratchCard();
                } else if (!document.getElementById('lottery-main').classList.contains('hidden')) {
                    this.closeLottery();
                }
            }
        });
    }
    
    // 打开彩票店
    openLottery(data) {
        this.isOpen = true;
        this.currentShop = data.shopData; // 修正为shopData
        this.playerData = data.playerData; // 修正为playerData
        this.config = data.config;

        document.getElementById('lottery-main').classList.remove('hidden');

        // 初始化球号选择网格
        this.initBallGrids();

        // 更新价格显示
        this.updatePriceDisplay();

        // 只加载奖池信息，因为主页面需要显示奖池数据
        this.loadPrizePools();

        // 不再自动加载历史记录和未兑奖奖品，改为按需加载
        // this.loadHistory();
        // this.loadUnclaimedPrizes();
    }
    
    // 更新价格显示
    updatePriceDisplay() {
        if (!this.config) {
            return;
        }
        
        try {
            // 更新刮刮乐价格
            if (this.config.scratchCards) {
                Object.entries(this.config.scratchCards).forEach(([cardType, cardConfig]) => {
                    const cardElement = document.querySelector(`.card-item[data-card="${cardType}"]`);
                    if (cardElement) {
                        const priceElement = cardElement.querySelector('.price');
                        const maxPrizeElement = cardElement.querySelector('.max-prize');
                        
                        if (priceElement) {
                            priceElement.textContent = `¥${cardConfig.price}`;
                        }
                        
                        if (maxPrizeElement && cardConfig.maxPrize) {
                            maxPrizeElement.textContent = `最高奖金：¥${this.formatNumber(cardConfig.maxPrize)}`;
                        }
                    }
                });
            }
            
            // 更新双色球价格
            if (this.config.doubleBall && this.config.doubleBall.price) {
                const buyButton = document.getElementById('buy-double-ball');
                if (buyButton) {
                    buyButton.innerHTML = `<i class="fas fa-shopping-cart"></i> 购买 (¥${this.config.doubleBall.price})`;
                }
            }
            
            // 更新大乐透价格
            if (this.config.superLotto && this.config.superLotto.price) {
                const buyButton = document.getElementById('buy-super-lotto');
                if (buyButton) {
                    buyButton.innerHTML = `<i class="fas fa-shopping-cart"></i> 购买 (¥${this.config.superLotto.price})`;
                }
            }

            // 更新排列5价格
            if (this.config.arrangeFive && this.config.arrangeFive.price) {
                const buyButton = document.getElementById('buy-arrange-five');
                if (buyButton) {
                    buyButton.innerHTML = `<i class="fas fa-shopping-cart"></i> 购买 (¥${this.config.arrangeFive.price})`;
                }
            }
        } catch (error) {
            // 忽略错误
        }
    }
    
    // 关闭彩票店
    closeLottery() {
        document.getElementById('lottery-main').classList.add('hidden');
        this.isOpen = false;
        this.playSound('click');
        
        // 发送关闭消息到FiveM
        fetch(`https://${this.GetParentResourceName()}/closeLottery`, {
            method: 'POST',
            body: JSON.stringify({})
        });
    }
    
    // 切换标签页
    switchTab(tabName) {
        // 移除所有活动状态
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        // 激活选中的标签
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}-tab`).classList.add('active');
        
        this.playSound('click');
        
        // 根据标签页加载相应数据
        if (tabName === 'history') {
            this.loadHistory();
        } else if (tabName === 'claim') {
            this.loadUnclaimedPrizes();
        }
    }
    
    // 切换历史记录标签
    switchHistoryTab(historyType) {
        document.querySelectorAll('.history-nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelectorAll('.history-section').forEach(section => {
            section.classList.remove('active');
        });
        
        document.querySelector(`[data-history="${historyType}"]`).classList.add('active');
        
        if (historyType === 'scratch') {
            document.getElementById('scratch-history').classList.add('active');
        } else if (historyType === 'draw') {
            document.getElementById('draw-history').classList.add('active');
        } else {
            document.getElementById('lottery-history').classList.add('active');
        }
        
        this.loadSpecificHistory(historyType);
    }
    
    // 初始化球号网格
    initBallGrids() {
        // 双色球红球
        const redGrid = document.getElementById('red-balls-grid');
        redGrid.innerHTML = '';
        for (let i = 1; i <= this.config.doubleBall.redBalls; i++) {
            const ball = document.createElement('div');
            ball.className = 'ball';
            ball.textContent = i.toString().padStart(2, '0');
            ball.addEventListener('click', () => this.selectRedBall(i, ball));
            redGrid.appendChild(ball);
        }
        
        // 双色球蓝球
        const blueGrid = document.getElementById('blue-balls-grid');
        blueGrid.innerHTML = '';
        for (let i = 1; i <= this.config.doubleBall.blueBalls; i++) {
            const ball = document.createElement('div');
            ball.className = 'ball';
            ball.textContent = i.toString().padStart(2, '0');
            ball.addEventListener('click', () => this.selectBlueBall(i, ball));
            blueGrid.appendChild(ball);
        }
        
        // 大乐透前区球
        const frontGrid = document.getElementById('front-balls-grid');
        frontGrid.innerHTML = '';
        for (let i = 1; i <= this.config.superLotto.frontBalls; i++) {
            const ball = document.createElement('div');
            ball.className = 'ball';
            ball.textContent = i.toString().padStart(2, '0');
            ball.addEventListener('click', () => this.selectFrontBall(i, ball));
            frontGrid.appendChild(ball);
        }
        
        // 大乐透后区球
        const backGrid = document.getElementById('back-balls-grid');
        backGrid.innerHTML = '';
        for (let i = 1; i <= this.config.superLotto.backBalls; i++) {
            const ball = document.createElement('div');
            ball.className = 'ball';
            ball.textContent = i.toString().padStart(2, '0');
            ball.addEventListener('click', () => this.selectBackBall(i, ball));
            backGrid.appendChild(ball);
        }

        // 初始化排列5显示
        this.updateArrangeFiveDisplay();
    }
    
    // 选择红球
    selectRedBall(number, element) {
        const index = this.selectedNumbers.red.indexOf(number);
        
        if (index === -1) {
            if (this.selectedNumbers.red.length < this.config.doubleBall.selectRed) {
                this.selectedNumbers.red.push(number);
                element.classList.add('selected');
                this.playSound('click');
            } else {
                this.showMessage('最多只能选择6个红球', 'warning');
            }
        } else {
            this.selectedNumbers.red.splice(index, 1);
            element.classList.remove('selected');
            this.playSound('click');
        }
        
        document.getElementById('red-count').textContent = this.selectedNumbers.red.length;
    }
    
    // 选择蓝球
    selectBlueBall(number, element) {
        // 清除之前选择
        document.querySelectorAll('#blue-balls-grid .ball').forEach(ball => {
            ball.classList.remove('selected');
        });
        
        this.selectedNumbers.blue = number;
        element.classList.add('selected');
        document.getElementById('blue-count').textContent = 1;
        this.playSound('click');
    }
    
    // 选择前区球
    selectFrontBall(number, element) {
        const index = this.selectedNumbers.front.indexOf(number);
        
        if (index === -1) {
            if (this.selectedNumbers.front.length < this.config.superLotto.selectFront) {
                this.selectedNumbers.front.push(number);
                element.classList.add('selected');
                this.playSound('click');
            } else {
                this.showMessage('最多只能选择5个前区球', 'warning');
            }
        } else {
            this.selectedNumbers.front.splice(index, 1);
            element.classList.remove('selected');
            this.playSound('click');
        }
        
        document.getElementById('front-count').textContent = this.selectedNumbers.front.length;
    }
    
    // 选择后区球
    selectBackBall(number, element) {
        const index = this.selectedNumbers.back.indexOf(number);
        
        if (index === -1) {
            if (this.selectedNumbers.back.length < this.config.superLotto.selectBack) {
                this.selectedNumbers.back.push(number);
                element.classList.add('selected');
                this.playSound('click');
            } else {
                this.showMessage('最多只能选择2个后区球', 'warning');
            }
        } else {
            this.selectedNumbers.back.splice(index, 1);
            element.classList.remove('selected');
            this.playSound('click');
        }
        
        document.getElementById('back-count').textContent = this.selectedNumbers.back.length;
    }
    
    // 随机选择双色球
    randomDoubleBall() {
        this.clearDoubleBall();
        
        // 随机选择6个红球
        const redNumbers = [];
        while (redNumbers.length < this.config.doubleBall.selectRed) {
            const num = Math.floor(Math.random() * this.config.doubleBall.redBalls) + 1;
            if (!redNumbers.includes(num)) {
                redNumbers.push(num);
            }
        }
        
        // 随机选择1个蓝球
        const blueNumber = Math.floor(Math.random() * this.config.doubleBall.blueBalls) + 1;
        
        // 应用选择
        redNumbers.forEach(num => {
            const ball = document.querySelector(`#red-balls-grid .ball:nth-child(${num})`);
            this.selectRedBall(num, ball);
        });
        
        const blueBall = document.querySelector(`#blue-balls-grid .ball:nth-child(${blueNumber})`);
        this.selectBlueBall(blueNumber, blueBall);
        
        this.playSound('click');
    }
    
    // 清空双色球选择
    clearDoubleBall() {
        this.selectedNumbers.red = [];
        this.selectedNumbers.blue = null;
        
        document.querySelectorAll('#red-balls-grid .ball, #blue-balls-grid .ball').forEach(ball => {
            ball.classList.remove('selected');
        });
        
        document.getElementById('red-count').textContent = 0;
        document.getElementById('blue-count').textContent = 0;
        
        this.playSound('click');
    }
    
    // 随机选择大乐透
    randomSuperLotto() {
        this.clearSuperLotto();
        
        // 随机选择5个前区球
        const frontNumbers = [];
        while (frontNumbers.length < this.config.superLotto.selectFront) {
            const num = Math.floor(Math.random() * this.config.superLotto.frontBalls) + 1;
            if (!frontNumbers.includes(num)) {
                frontNumbers.push(num);
            }
        }
        
        // 随机选择2个后区球
        const backNumbers = [];
        while (backNumbers.length < this.config.superLotto.selectBack) {
            const num = Math.floor(Math.random() * this.config.superLotto.backBalls) + 1;
            if (!backNumbers.includes(num)) {
                backNumbers.push(num);
            }
        }
        
        // 应用选择
        frontNumbers.forEach(num => {
            const ball = document.querySelector(`#front-balls-grid .ball:nth-child(${num})`);
            this.selectFrontBall(num, ball);
        });
        
        backNumbers.forEach(num => {
            const ball = document.querySelector(`#back-balls-grid .ball:nth-child(${num})`);
            this.selectBackBall(num, ball);
        });
        
        this.playSound('click');
    }
    
    // 清空大乐透选择
    clearSuperLotto() {
        this.selectedNumbers.front = [];
        this.selectedNumbers.back = [];
        
        document.querySelectorAll('#front-balls-grid .ball, #back-balls-grid .ball').forEach(ball => {
            ball.classList.remove('selected');
        });
        
        document.getElementById('front-count').textContent = 0;
        document.getElementById('back-count').textContent = 0;
        
        this.playSound('click');
    }
    
    // 购买刮刮乐
    buyScratchCard(cardType) {
        this.showLoading(true);
        
        fetch(`https://${this.GetParentResourceName()}/buyScratchCard`, {
            method: 'POST',
            body: JSON.stringify({ cardType })
        })
        .then(response => response.json())
        .catch(() => {
            this.showLoading(false);
        });
    }
    
    // 购买双色球
    buyDoubleBall() {
        if (this.selectedNumbers.red.length !== this.config.doubleBall.selectRed) {
            this.showMessage('请选择6个红球', 'warning');
            return;
        }
        
        if (!this.selectedNumbers.blue) {
            this.showMessage('请选择1个蓝球', 'warning');
            return;
        }
        
        this.showLoading(true);
        
        fetch(`https://${this.GetParentResourceName()}/buyDoubleBall`, {
            method: 'POST',
            body: JSON.stringify({
                redBalls: this.selectedNumbers.red,
                blueBall: this.selectedNumbers.blue
            })
        })
        .then(response => response.json())
        .catch(() => {
            this.showLoading(false);
        });
    }
    
    // 购买大乐透
    buySuperLotto() {
        if (this.selectedNumbers.front.length !== this.config.superLotto.selectFront) {
            this.showMessage('请选择5个前区球', 'warning');
            return;
        }
        
        if (this.selectedNumbers.back.length !== this.config.superLotto.selectBack) {
            this.showMessage('请选择2个后区球', 'warning');
            return;
        }
        
        this.showLoading(true);
        
        fetch(`https://${this.GetParentResourceName()}/buySuperLotto`, {
            method: 'POST',
            body: JSON.stringify({
                frontBalls: this.selectedNumbers.front,
                backBalls: this.selectedNumbers.back
            })
        })
        .then(response => response.json())
        .catch(() => {
            this.showLoading(false);
        });
    }
    
    // 预加载刮刮乐资源
    prepareResources(cardType) {
        this.resourcesLoaded = false;
        
        // 根据卡片类型预加载相应图片
        const imagesToPreload = [];
        if (cardType === 'scratch_xixiangfeng') {
            imagesToPreload.push('images/xixiangfeng.png', 'images/xixiangfeng1.png');
        } else if (cardType === 'scratch_fusong') {
            imagesToPreload.push('images/fusong.png', 'images/fusong1.png');
        } else if (cardType === 'scratch_yaocai') {
            imagesToPreload.push('images/yaocai.png', 'images/yaocai1.png');
        } else if (cardType === 'scratch_caizuan') {
            imagesToPreload.push('images/caizuan.png', 'images/caizuan1.png');
        } else if (cardType === 'scratch_zhongguofu') {
            imagesToPreload.push('images/fu.png', 'images/fu1.png');
        } else if (cardType === 'scratch_chengfeng') {
            imagesToPreload.push('images/chengfeng.png', 'images/chengfeng1.png');
        }
        
        // 预加载图片
        let loadedCount = 0;
        imagesToPreload.forEach(src => {
            const img = new Image();
            img.onload = () => {
                loadedCount++;
                if (loadedCount === imagesToPreload.length) {
                    this.resourcesLoaded = true;
                }
            };
            img.onerror = () => {
                loadedCount++;
                // 即使失败也继续处理
                if (loadedCount === imagesToPreload.length) {
                    this.resourcesLoaded = true;
                }
            };
            img.src = src;
        });
        
        // 设置超时，确保即使图片加载失败也能继续
        setTimeout(() => {
            if (!this.resourcesLoaded) {
                this.resourcesLoaded = true;
            }
        }, 3000);
    }
    
    // 打开刮刮乐游戏
    openScratchCard(data) {
        // 存储数据
        this.scratchData = data;
        
        // 重置自动兑奖标志
        this.autoClaimSent = false;
        
        // 显示刮刮乐界面
        document.getElementById('scratch-game').classList.remove('hidden');
        document.getElementById('scratch-title').textContent = data.cardName || "刮刮乐";
        
        // 确保画布正确初始化
        this.setupScratchGame(data);
        
        // 增加延迟确保UI完全加载，防止首次使用时闪退
        setTimeout(() => {
            if (this.scratchCanvas && this.scratchContext) {
                // 重置画布状态，确保图像正确加载
                this.resetScratchCanvas();
                
                // 延迟通知客户端准备完毕
                setTimeout(() => {
                    // 检查是否有数据
                    if (!this.scratchData) {
                        return;
                    }
                    
                    // 确保图像已经完全加载到画布
                    this.checkCanvasReady(() => {
                        fetch(`https://${this.GetParentResourceName()}/focus`, {
                            method: 'POST',
                            body: JSON.stringify({ focus: true })
                        }).catch(() => {});
                        
                        this.playSound('click');
                    });
                }, 200);
            }
        }, 300);
    }
    
    // 检查画布是否准备就绪
    checkCanvasReady(callback) {
        try {
            // 尝试获取画布数据，如果成功则表示画布已准备好
            if (this.scratchContext) {
                this.scratchContext.getImageData(0, 0, 1, 1);
                callback && callback();
            } else {
                setTimeout(() => this.checkCanvasReady(callback), 100);
            }
        } catch (e) {
            // 如果失败，延迟后再试
            setTimeout(() => this.checkCanvasReady(callback), 100);
        }
    }
    
    // 关闭刮刮乐游戏
    closeScratchCard() {
        document.getElementById('scratch-game').classList.add('hidden');

        // 重置完成图片状态
        const completedImageContainer = document.getElementById('scratch-completed-image');
        if (completedImageContainer) {
            completedImageContainer.style.backgroundImage = '';
        }

        this.scratchData = null;
        this.autoClaimSent = false;  // 重置自动兑奖标志
        this.playSound('click');

        fetch(`https://${this.GetParentResourceName()}/closeScratchCard`, {
            method: 'POST',
            body: JSON.stringify({})
        });
    }
    
    // 设置刮刮乐游戏
    setupScratchGame(data) {
        const areasContainer = document.getElementById('scratch-areas');
        areasContainer.innerHTML = '';
        // 动态设置中奖内容class
        areasContainer.className = '';
        
        // 设置初始图片
        const completedImageContainer = document.getElementById('scratch-completed-image');
        if (completedImageContainer) {
            let imageName = '';
            if (data.cardType === 'scratch_xixiangfeng') {
                imageName = 'xixiangfeng1.png';
            } else if (data.cardType === 'scratch_fusong') {
                imageName = 'fusong1.png';
            } else if (data.cardType === 'scratch_yaocai') {
                imageName = 'yaocai1.png';
            } else if (data.cardType === 'scratch_caizuan') {
                imageName = 'caizuan1.png';
            } else if (data.cardType === 'scratch_zhongguofu') {
                imageName = 'fu1.png';
            } else if (data.cardType === 'scratch_chengfeng') {
                imageName = 'chengfeng1.png';
            }
            completedImageContainer.style.backgroundImage = imageName ? `url('images/${imageName}')` : '';
        }
        
        if (data.cardType === 'scratch_xixiangfeng') {
            areasContainer.classList.add('xixiangfeng');
            // 喜相逢：10个区域，2行5列
            areasContainer.style.gridTemplateColumns = 'repeat(5, 1fr)';
            areasContainer.style.gridTemplateRows = 'repeat(2, 1fr)';
            
            // 创建2x5网格的刮奖区域
            for (let row = 0; row < 2; row++) {
                for (let col = 0; col < 5; col++) {
                    const index = row * 5 + col;
                    if (index < 10) { // 确保不超出数据范围
                        const area = document.createElement('div');
                        area.className = 'scratch-area';
                        area.dataset.index = index;
                        
                        // 使用div包装内容，便于定位在圆形区域内
                        area.innerHTML = `
                            <div class="scratch-content">
                                <div class="scratch-symbol">${data.ticketData.areas[index].symbol}</div>
                                <div class="scratch-amount">¥${data.ticketData.areas[index].amount}</div>
                            </div>
                        `;
                        areasContainer.appendChild(area);
                    }
                }
            }
        } else if (data.cardType === 'scratch_fusong') {
            areasContainer.classList.add('fusong');
            this.setupFuSongGame(data.ticketData, areasContainer);
        } else if (data.cardType === 'scratch_yaocai') {
            areasContainer.classList.add('yaocai');
            this.setupYaoCaiGame(data.ticketData, areasContainer);
        } else if (data.cardType === 'scratch_caizuan') {
            areasContainer.classList.add('caizuan');
            this.setupCaiZuanGame(data.ticketData, areasContainer);
        } else if (data.cardType === 'scratch_zhongguofu') {
            areasContainer.classList.add('zhongguofu');
            this.setupZhongGuoFuGame(data.ticketData, areasContainer);
        } else if (data.cardType === 'scratch_chengfeng') {
            areasContainer.classList.add('chengfeng');
            this.setupChengFengGame(data.ticketData, areasContainer);
        }
        this.resetScratchCanvas();
    }
    
    // 设置刮刮乐画布
    setupScratchCanvas() {
        this.scratchCanvas = document.getElementById('scratch-canvas');
        if (this.scratchCanvas) {
            this.scratchContext = this.scratchCanvas.getContext('2d');
            this.bindScratchEvents();
            
            // 确保画布正确初始化后请求焦点
            if (!document.getElementById('scratch-game').classList.contains('hidden')) {
                setTimeout(() => {
                    fetch(`https://${this.GetParentResourceName()}/focus`, {
                        method: 'POST',
                        body: JSON.stringify({ focus: true })
                    });
                }, 100);
            }
        }
    }
    
    // 重置刮刮乐画布，加载未刮开图片作为涂层
    resetScratchCanvas() {
        if (!this.scratchContext || !this.scratchData) {
            return;
        }

        // 完成图片始终在底层显示，不需要隐藏

        const cardType = this.scratchData.cardType;
        let coverImg = new Image();
        let coverImgSrc = '';
        
        // 根据卡片类型选择未刮开图片
        if (cardType === 'scratch_xixiangfeng') {
            coverImgSrc = 'images/xixiangfeng.png';
        } else if (cardType === 'scratch_fusong') {
            coverImgSrc = 'images/fusong.png';
        } else if (cardType === 'scratch_yaocai') {
            coverImgSrc = 'images/yaocai.png';
        } else if (cardType === 'scratch_caizuan') {
            coverImgSrc = 'images/caizuan.png';
        } else if (cardType === 'scratch_zhongguofu') {
            coverImgSrc = 'images/fu.png';
        } else if (cardType === 'scratch_chengfeng') {
            coverImgSrc = 'images/chengfeng.png';
        } else {
            // 默认灰色
            this.scratchContext.fillStyle = '#888';
            this.scratchContext.fillRect(0, 0, this.scratchCanvas.width, this.scratchCanvas.height);
            this.scratchContext.globalCompositeOperation = 'destination-out';
            return;
        }
        
        // 预先设置画布尺寸，避免重新渲染导致闪烁
        this.scratchCanvas.width = 720;
        this.scratchCanvas.height = 720;
        this.scratchCanvas.style.width = '720px';
        this.scratchCanvas.style.height = '720px';
        
        // 先用默认颜色填充避免画布空白
        this.scratchContext.globalCompositeOperation = 'source-over';
        this.scratchContext.fillStyle = '#888';
        this.scratchContext.fillRect(0, 0, 720, 720);
        
        // 创建一个Promise来处理图片加载
        const loadImage = () => {
            return new Promise((resolve, reject) => {
                coverImg = new Image();
                coverImg.crossOrigin = "anonymous"; // 解决跨域问题
                
                coverImg.onload = () => {
                    resolve(coverImg);
                };
                
                coverImg.onerror = (e) => {
                    reject(e);
                };
                
                // 设置超时
                setTimeout(() => {
                    if (!coverImg.complete) {
                        reject(new Error("图片加载超时"));
                    }
                }, 5000);
                
                coverImg.src = coverImgSrc;
            });
        };
        
        // 尝试加载图片，如果失败则使用备用方案
        loadImage()
            .then(img => {
                // 成功加载图片
                try {
                    this.scratchContext.globalCompositeOperation = 'source-over';
                    this.scratchContext.clearRect(0, 0, 720, 720);
    
                    // 计算缩放比例，完整显示图片且居中
                    const imgW = img.width;
                    const imgH = img.height;
                    const scale = Math.min(720 / imgW, 720 / imgH);
                    const drawW = imgW * scale;
                    const drawH = imgH * scale;
                    const offsetX = (720 - drawW) / 2;
                    const offsetY = (720 - drawH) / 2;
    
                    this.scratchContext.drawImage(img, offsetX, offsetY, drawW, drawH);
                    this.scratchContext.globalCompositeOperation = 'destination-out';
                } catch(e) {
                    // 出错时使用备用方案
                    this.useFallbackCanvas();
                }
            })
            .catch(() => {
                this.useFallbackCanvas();
            });
    }
    
    // 使用备用画布（当图片加载失败时）
    useFallbackCanvas() {
        if (!this.scratchContext) return;
        this.scratchContext.globalCompositeOperation = 'source-over';
        this.scratchContext.fillStyle = '#888';
        this.scratchContext.fillRect(0, 0, 720, 720);
        this.scratchContext.globalCompositeOperation = 'destination-out';
    }
    
    // 绑定刮奖事件
    bindScratchEvents() {
        let isScratching = false;
        
        this.scratchCanvas.addEventListener('mousedown', (e) => {
            isScratching = true;
            this.scratch(e);
        });
        
        this.scratchCanvas.addEventListener('mousemove', (e) => {
            if (isScratching) {
                this.scratch(e);
            }
        });
        
        this.scratchCanvas.addEventListener('mouseup', () => {
            isScratching = false;
            // 检查刮开区域百分比（使用防抖定时器）
            if (this.scratchCheckTimeout) {
                clearTimeout(this.scratchCheckTimeout);
            }
            this.scratchCheckTimeout = setTimeout(() => {
                this.checkScratchPercentage();
            }, 100);
        });
        
        // 触摸事件
        this.scratchCanvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            isScratching = true;
            this.scratch(e.touches[0]);
        });
        
        this.scratchCanvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            if (isScratching) {
                this.scratch(e.touches[0]);
            }
        });
        
        this.scratchCanvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            isScratching = false;
            // 检查刮开区域百分比（使用防抖定时器）
            if (this.scratchCheckTimeout) {
                clearTimeout(this.scratchCheckTimeout);
            }
            this.scratchCheckTimeout = setTimeout(() => {
                this.checkScratchPercentage();
            }, 100);
        });
    }
    
    // 检查刮开区域的百分比
    checkScratchPercentage() {
        if (!this.scratchContext || !this.scratchCanvas) return;
        
        // 获取画布数据
        let scratchAreaWidth, scratchAreaHeight, scratchAreaLeft, scratchAreaTop, scratchAreaBottom;
        
        // 所有类型的刮刮乐都使用相同的逻辑检查刮开区域百分比
        // 获取刮开区域的像素数据
        try {
            const imageData = this.scratchContext.getImageData(
                0, 
                0, 
                this.scratchCanvas.width, 
                this.scratchCanvas.height
            );
            
            // 计算透明像素的数量
            let transparentPixels = 0;
            const totalPixels = imageData.data.length / 4; // RGBA数据，每个像素4个值
            
            for (let i = 0; i < imageData.data.length; i += 4) {
                // 检查Alpha通道，0表示完全透明
                if (imageData.data[i + 3] === 0) {
                    transparentPixels++;
                }
            }
            
            const scratchedPercentage = (transparentPixels / totalPixels) * 100;
            
            // 如果刮开区域超过70%，自动显示全部，但仅在未发送过自动兑奖请求时
            if (scratchedPercentage > 70 && !this.autoClaimSent) {
                this.revealAll();
            }
        } catch (error) {
            // 处理错误
        }
    }
    
    // 全部显示
    revealAll() {
        if (!this.scratchContext) return;
        
        // 移除喜相逢类型的区域限制，所有类型都直接清除整个画布
        this.scratchContext.clearRect(0, 0, this.scratchCanvas.width, this.scratchCanvas.height);
            
        // 显示刮完奖后的图片
        this.showCompletedImage();
        
        this.playSound('click');
        
        // 确保鼠标焦点保持
        fetch(`https://${this.GetParentResourceName()}/focus`, {
            method: 'POST',
            body: JSON.stringify({ focus: true })
        }).catch(() => {});
        
        // 如果启用了自动兑奖，在显示全部后自动处理兑奖
        if (this.scratchData && !this.autoClaimSent) {
            // 设置标志，防止重复发送
            this.autoClaimSent = true;
            
            // 使用标准fetch API发送请求
            fetch(`https://${this.GetParentResourceName()}/autoClaimOnReveal`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8'
                },
                body: JSON.stringify({
                    ticketId: this.scratchData.ticketId,
                    cardType: this.scratchData.cardType
                })
            }).catch(() => {});
        }
    }
    
    // 显示刮完奖后的图片
    showCompletedImage() {
        if (!this.scratchData) return;
        
        const completedImageContainer = document.getElementById('scratch-completed-image');
        if (!completedImageContainer) return;
        
        let imageName = '';
            
        // 根据卡片类型选择对应的刮完奖图片
        if (this.scratchData.cardType === 'scratch_xixiangfeng') {
            imageName = 'xixiangfeng1.png';
        } else if (this.scratchData.cardType === 'scratch_fusong') {
            imageName = 'fusong1.png';
        } else if (this.scratchData.cardType === 'scratch_yaocai') {
            imageName = 'yaocai1.png';
        } else if (this.scratchData.cardType === 'scratch_caizuan') {
            imageName = 'caizuan1.png';
        } else if (this.scratchData.cardType === 'scratch_zhongguofu') {
            imageName = 'fu1.png';
        } else if (this.scratchData.cardType === 'scratch_chengfeng') {
            imageName = 'chengfeng1.png';
        } else {
            return; // 未知类型不显示
        }
        
        // 设置背景图片（完成图片始终在底层显示）
        completedImageContainer.style.backgroundImage = `url('images/${imageName}')`;
    }
    
    // 完成刮奖
    finishScratch() {
        if (!this.scratchData) return;
        
        this.showLoading(true);
        
        fetch(`https://${this.GetParentResourceName()}/finishScratch`, {
            method: 'POST',
            body: JSON.stringify({
                ticketId: this.scratchData.ticketId,
                cardType: this.scratchData.cardType
            })
        })
        .then(response => response.json())
        .then(data => {
            this.showLoading(false);
            if (data.success) {
                // 等待服务器返回结果
            }
        })
        .catch(() => {
            this.showLoading(false);
            this.showMessage('操作失败', 'error');
        });
    }
    
    // 显示刮奖结果
    showScratchResult(result) {
        // 移除可能导致闪退的自动关闭代码
        // 确保UI元素已加载和可见
        if (!result) {
            return;
        }
        
        // 兼容 prizeAmount 字段不存在的情况
        const prizeAmount = result.prizeAmount || result.prize_amount || result.amount || 0;

        // 检查是否中奖：金钱奖励 > 0 或者 isWinning = true（物品奖励）
        const isWinning = prizeAmount > 0 || result.isWinning === true;

        // 确保使用setTimeout延迟显示结果，避免界面卡顿
        setTimeout(() => {
            if (isWinning) {
                // 检查是否存在支付失败标志
                if (result.paymentFailed) {
                    // 显示账户余额不足消息
                    this.showAnnouncement(result.message || "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", 'error');
                    this.playSound('alert');
                } else {
                    // 根据奖励类型显示不同的中奖消息
                    if (prizeAmount > 0) {
                        // 金钱奖励
                        this.showAnnouncement(`恭喜中奖！奖金：¥${prizeAmount}`, 'win');
                    } else if (result.itemRewards && result.itemRewards.length > 0) {
                        // 物品奖励
                        this.showAnnouncement(`恭喜中奖！获得物品：${result.itemRewards.join(', ')}`, 'win');
                    } else {
                        // 其他中奖情况
                        this.showAnnouncement('恭喜中奖！', 'win');
                    }
                    this.playSound('win');
                }
            } else {
                this.showMessage('很遗憾，没有中奖', 'warning');
                this.playSound('lose');

                // 确保非中奖情况下也正确保持焦点
                if (!document.getElementById('scratch-game').classList.contains('hidden')) {
                    fetch(`https://${this.GetParentResourceName()}/focus`, {
                        method: 'POST',
                        body: JSON.stringify({ focus: true })
                    }).catch(() => {});
                }
            }
        }, 100);
    }
    
    // 加载奖池信息
    loadPrizePools() {
        fetch(`https://${this.GetParentResourceName()}/getPrizePools`, {
            method: 'POST',
            body: JSON.stringify({})
        })
        .then(response => {
            return response.json();
        })
        .then(data => {
            if (data && Array.isArray(data) && data.length > 0) {
                this.updatePrizePools(data);
            } else {
                // 显示默认值
                const defaultPools = [
                    { lottery_type: 'double_ball', current_amount: 5000000 },
                    { lottery_type: 'super_lotto', current_amount: 10000000 }
                ];
                this.updatePrizePools(defaultPools);
            }
        })
        .catch(() => {
            // 显示默认值
            const defaultPools = [
                { lottery_type: 'double_ball', current_amount: 5000000 },
                { lottery_type: 'super_lotto', current_amount: 10000000 },
                { lottery_type: 'arrange_five', current_amount: 1000000 }
            ];
            this.updatePrizePools(defaultPools);
        });
    }
    
    // 处理更新奖池信息
    updatePrizePools(pools) {
        if (!pools || !Array.isArray(pools) || pools.length === 0) {
            // 使用默认值
            pools = [
                { lottery_type: 'double_ball', current_amount: 5000000 },
                { lottery_type: 'super_lotto', current_amount: 10000000 },
                { lottery_type: 'arrange_five', current_amount: 1000000 }
            ];
        }
        
        pools.forEach(pool => {
            if (!pool.lottery_type || pool.current_amount === undefined) {
                return;
            }
            
            const elementId = `${pool.lottery_type.replace('_', '-')}-pool`;
            const element = document.getElementById(elementId);
            
            if (element) {
                // 直接使用实际金额，不再替换为默认值
                let amount = pool.current_amount;
                element.textContent = `¥${this.formatNumber(amount)}`;
            }
        });
    }
    
    // 加载历史记录
    loadHistory() {
        // 不再自动加载任何数据，等用户切换到具体的历史标签时再按需加载
        // 检查当前激活的历史标签，如果有的话就加载对应数据
        const activeHistoryBtn = document.querySelector('.history-nav-btn.active');
        if (activeHistoryBtn) {
            const historyType = activeHistoryBtn.dataset.history;
            this.loadSpecificHistory(historyType);
        } else {
            // 如果没有激活的标签，默认激活刮刮乐标签但不加载数据
            const scratchBtn = document.querySelector('[data-history="scratch"]');
            if (scratchBtn) {
                scratchBtn.classList.add('active');
                document.getElementById('scratch-history').classList.add('active');
            }
        }
    }
    
    // 渲染刮刮乐统计数据
    renderScratchStats(stats) {
        let totalCount = 0;
        let totalPrize = 0;
        
        if (stats && Array.isArray(stats) && stats.length > 0) {
            // 只在调试模式下输出日志
            if (this.config && this.config.debug === true) {
                console.log("原始刮刮乐统计数据:", JSON.stringify(stats));
            }
            
            stats.forEach(stat => {
                // 确保count和total_prize为数字
                const count = typeof stat.count === 'number' ? stat.count : parseInt(stat.count || 0);
                const prize = typeof stat.total_prize === 'number' ? stat.total_prize : parseFloat(stat.total_prize || 0);
                
                totalCount += count;
                totalPrize += prize;
                
                // 只在调试模式下输出日志
                if (this.config && this.config.debug === true) {
                    console.log(`卡片类型: ${stat.card_type}, 数量: ${count}, 奖金: ${prize}, 累计总数: ${totalCount}, 累计奖金: ${totalPrize}`);
                }
            });
            
            // 只在调试模式下输出日志
            if (this.config && this.config.debug === true) {
                console.log(`最终统计 - 总数: ${totalCount}, 总奖金: ${totalPrize}`);
            }
            
            document.getElementById('scratch-total').textContent = `${totalCount}张`;
            document.getElementById('scratch-prize').textContent = `¥${this.formatNumber(totalPrize)}`;
            
            // 渲染刮刮乐历史记录列表
            this.renderHistoryList(stats, 'scratch-history-list', 'scratch');
        } else {
            document.getElementById('scratch-total').textContent = `0张`;
            document.getElementById('scratch-prize').textContent = `¥0`;
            
            // 显示暂无记录
            const container = document.getElementById('scratch-history-list');
            if (container) {
                container.innerHTML = '<p style="text-align: center; color: rgba(255,255,255,0.6);">暂无记录</p>';
            }
        }
    }
    
    // 加载特定历史记录
    loadSpecificHistory(type) {
        let endpoint = '';
        let listElement = '';
        let lotteryType = '';
        
        switch (type) {
            case 'scratch':
                endpoint = 'getScratchStats';
                listElement = 'scratch-history-list';
                lotteryType = 'scratch';
                break;
            case 'double-ball':
                endpoint = 'getPlayerTickets';
                listElement = 'lottery-history-list';
                lotteryType = 'double_ball';
                break;
            case 'super-lotto':
                endpoint = 'getPlayerTickets';
                listElement = 'lottery-history-list';
                lotteryType = 'super_lotto';
                break;
            case 'arrange-five':
                endpoint = 'getPlayerTickets';
                listElement = 'lottery-history-list';
                lotteryType = 'arrange_five';
                break;
            case 'draw':
                // 对于开奖历史，我们需要加载两种类型的数据
                const container = document.getElementById('draw-history-list');
                if (container) {
                    container.innerHTML = '<p style="text-align: center; color: rgba(255,255,255,0.6);">加载中...</p>';
                }
                
                // 重置历史数据，确保每次切换到此标签时都重新加载
                this.drawHistoryData = [];
                
                // 先加载双色球数据
                this.loadDrawHistoryByType('double_ball');
                // 然后加载大乐透数据
                this.loadDrawHistoryByType('super_lotto');
                // 最后加载排列5数据
                this.loadDrawHistoryByType('arrange_five');
                return; // 直接返回，不执行下面的代码
            default:
                return; // 未知类型，直接返回
        }
        
        if (!endpoint) return;
        
        // 清空列表并显示加载中
        const container = document.getElementById(listElement);
        if (container) {
            container.innerHTML = '<p style="text-align: center; color: rgba(255,255,255,0.6);">加载中...</p>';
            
            // 标记当前活动的历史类型，这样updatePlayerTickets事件知道更新哪个标签页
            if (type === 'double-ball' || type === 'super-lotto' || type === 'arrange-five') {
                container.dataset.waitingData = 'true';
                container.dataset.historyType = type;
            }
        }
        
        fetch(`https://${this.GetParentResourceName()}/${endpoint}`, {
            method: 'POST',
            body: JSON.stringify({ lotteryType: lotteryType })
        })
        .then(response => response.json())
        .then(data => {
            // 如果数据是字符串，尝试解析为JSON
            if (typeof data === 'string') {
                try {
                    data = JSON.parse(data);
                } catch (e) {
                    // 解析失败，忽略错误
                }
            }
            
            // 确保data是数组
            if (!Array.isArray(data)) {
                data = Array.isArray(data) ? data : (data ? [data] : []);
            }
            
            // 如果还在等待服务器事件的数据，且为空数组，先不渲染
            if ((type === 'double-ball' || type === 'super-lotto' || type === 'arrange-five') && (!data || data.length === 0)) {
                return;
            }
            
            this.renderHistoryList(data, listElement, type);
        })
        .catch(() => {
            if (container) {
                container.innerHTML = '<p style="text-align: center; color: rgba(255,0,0,0.6);">加载失败</p>';
            }
        });
    }
    
    // 加载特定类型的开奖历史
    loadDrawHistoryByType(lotteryType) {
        // 存储已加载的数据
        if (!this.drawHistoryData) {
            this.drawHistoryData = [];
        }
        
        fetch(`https://${this.GetParentResourceName()}/getDrawHistory`, {
            method: 'POST',
            body: JSON.stringify({ lotteryType: lotteryType })
        })
        .then(response => response.json())
        .then(data => {
            // 确保data是数组
            if (!Array.isArray(data)) {
                data = Array.isArray(data) ? data : (data ? [data] : []);
            }
            
            // 合并数据，确保不会覆盖已有数据
            if (data && data.length > 0) {
                // 检查是否已经有相同ID的记录，避免重复
                const existingIds = this.drawHistoryData.map(item => item.id);
                const newData = data.filter(item => !existingIds.includes(item.id));
                
                // 合并新数据
                this.drawHistoryData = this.drawHistoryData.concat(newData);
                
                // 合并后按开奖日期降序排序
                this.drawHistoryData.sort((a, b) => {
                    // 获取日期对象
                    let dateA, dateB;
                    
                    // 处理a的日期
                    if (typeof a.draw_date === 'number') {
                        dateA = new Date(a.draw_date);
                    } else if (typeof a.draw_date === 'string') {
                        // 尝试解析为日期
                        dateA = this.parseMySQLDateTime(a.draw_date) || new Date(a.draw_date);
                    } else {
                        dateA = new Date(0); // 默认最早日期
                    }
                    
                    // 处理b的日期
                    if (typeof b.draw_date === 'number') {
                        dateB = new Date(b.draw_date);
                    } else if (typeof b.draw_date === 'string') {
                        // 尝试解析为日期
                        dateB = this.parseMySQLDateTime(b.draw_date) || new Date(b.draw_date);
                    } else {
                        dateB = new Date(0); // 默认最早日期
                    }
                    
                    // 降序排序（最新的在前）
                    return dateB - dateA;
                });
            }
            
            // 渲染合并后的数据
            this.renderHistoryList(this.drawHistoryData, 'draw-history-list', 'draw');
        })
        .catch(() => {
            // 如果是第一次加载失败，显示错误信息
            if (this.drawHistoryData.length === 0) {
                const container = document.getElementById('draw-history-list');
                if (container) {
                    container.innerHTML = '<p style="text-align: center; color: rgba(255,0,0,0.6);">加载失败</p>';
                }
            }
        });
    }
    
    // 格式化双色球号码为HTML圆球
    formatDoubleBallNumbers(numbers) {
        if (!numbers) return '未知';
        
        let redBalls = [];
        let blueBall = null;
        
        // 解析红球和蓝球
        if (typeof numbers === 'string' && numbers.includes('|')) {
            const parts = numbers.split('|');
            if (parts.length === 2) {
                redBalls = parts[0].trim().split(',').map(n => n.trim());
                blueBall = parts[1].trim();
            }
        } else if (typeof numbers === 'object') {
            if (numbers.redBalls && Array.isArray(numbers.redBalls)) {
                redBalls = numbers.redBalls.map(n => n.toString());
            } else if (numbers.redBalls && typeof numbers.redBalls === 'string') {
                redBalls = numbers.redBalls.split(',').map(n => n.trim());
            }
            
            if (numbers.blueBall) {
                blueBall = numbers.blueBall.toString();
            }
        }
        
        // 创建HTML
        let html = '';
        
        // 添加红球
        if (redBalls.length > 0) {
            redBalls.forEach(ball => {
                html += `<span class="history-ball red-ball">${ball}</span>`;
            });
        }
        
        // 添加分隔符
        if (blueBall) {
            html += `<span class="ball-separator">|</span>`;
        }
        
        // 添加蓝球
        if (blueBall) {
            html += `<span class="history-ball blue-ball">${blueBall}</span>`;
        }
        
        return html || '未知';
    }
    
    // 格式化大乐透号码为HTML圆球
    formatSuperLottoNumbers(numbers) {
        if (!numbers) return '未知';
        
        let frontBalls = [];
        let backBalls = [];
        
        // 解析前区和后区号码
        if (typeof numbers === 'string' && numbers.includes('|')) {
            const parts = numbers.split('|');
            if (parts.length === 2) {
                frontBalls = parts[0].trim().split(',').map(n => n.trim());
                backBalls = parts[1].trim().split(',').map(n => n.trim());
            }
        } else if (typeof numbers === 'object') {
            if (numbers.frontBalls && Array.isArray(numbers.frontBalls)) {
                frontBalls = numbers.frontBalls.map(n => n.toString());
            } else if (numbers.frontBalls && typeof numbers.frontBalls === 'string') {
                frontBalls = numbers.frontBalls.split(',').map(n => n.trim());
            }
            
            if (numbers.backBalls && Array.isArray(numbers.backBalls)) {
                backBalls = numbers.backBalls.map(n => n.toString());
            } else if (numbers.backBalls && typeof numbers.backBalls === 'string') {
                backBalls = numbers.backBalls.split(',').map(n => n.trim());
            }
        }
        
        // 创建HTML
        let html = '';
        
        // 添加前区号码
        if (frontBalls.length > 0) {
            frontBalls.forEach(ball => {
                html += `<span class="history-ball front-ball">${ball}</span>`;
            });
        }
        
        // 添加分隔符
        if (backBalls.length > 0) {
            html += `<span class="ball-separator">|</span>`;
        }
        
        // 添加后区号码
        if (backBalls.length > 0) {
            backBalls.forEach(ball => {
                html += `<span class="history-ball back-ball">${ball}</span>`;
            });
        }
        
        return html || '未知';
    }
    
    // 渲染历史记录列表
    renderHistoryList(data, elementId, type) {
        const container = document.getElementById(elementId);

        if (!container) {
            return;
        }

        // 清空容器
        container.innerHTML = '';

        // 如果没有数据，显示暂无记录
        if (!data || !Array.isArray(data) || data.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: rgba(255,255,255,0.6);">暂无记录</p>';
            return;
        }

        // 对数据进行排序，确保最新记录在最上面
        if (type === 'draw') {
            // 开奖历史按开奖日期降序排序
            data.sort((a, b) => {
                let dateA, dateB;

                // 处理a的日期
                if (typeof a.draw_date === 'number') {
                    dateA = new Date(a.draw_date);
                } else if (typeof a.draw_date === 'string') {
                    dateA = this.parseMySQLDateTime(a.draw_date) || new Date(a.draw_date);
                } else {
                    dateA = new Date(0);
                }

                // 处理b的日期
                if (typeof b.draw_date === 'number') {
                    dateB = new Date(b.draw_date);
                } else if (typeof b.draw_date === 'string') {
                    dateB = this.parseMySQLDateTime(b.draw_date) || new Date(b.draw_date);
                } else {
                    dateB = new Date(0);
                }

                // 降序排序（最新的在前），如果日期相同则按ID降序
                const dateDiff = dateB - dateA;
                if (dateDiff === 0) {
                    return (b.id || 0) - (a.id || 0);
                }
                return dateDiff;
            });
        } else if (type === 'double-ball' || type === 'super-lotto' || type === 'arrange-five') {
            // 彩票记录按购买时间降序排序
            data.sort((a, b) => {
                let dateA, dateB;

                // 处理a的日期
                if (typeof a.purchase_time === 'number') {
                    dateA = new Date(a.purchase_time);
                } else if (typeof a.purchase_time === 'string') {
                    dateA = this.parseMySQLDateTime(a.purchase_time) || new Date(a.purchase_time);
                } else {
                    dateA = new Date(0);
                }

                // 处理b的日期
                if (typeof b.purchase_time === 'number') {
                    dateB = new Date(b.purchase_time);
                } else if (typeof b.purchase_time === 'string') {
                    dateB = this.parseMySQLDateTime(b.purchase_time) || new Date(b.purchase_time);
                } else {
                    dateB = new Date(0);
                }

                // 降序排序（最新的在前），如果日期相同则按ID降序
                const dateDiff = dateB - dateA;
                if (dateDiff === 0) {
                    return (b.id || 0) - (a.id || 0);
                }
                return dateDiff;
            });
        }
        
        let renderedItems = 0;
        
        data.forEach((item, index) => {
            try {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';
                
                let content = '';
                if (type === 'scratch') {
                    content = `
                        <h4>${this.translateCardType(item.card_type)}</h4>
                        <p>数量: ${item.count}张</p>
                        <p>总奖金: ¥${this.formatNumber(item.total_prize || 0)}</p>
                    `;
                    renderedItems++;
                } else if (type === 'draw') {
                    // 处理开奖日期显示
                    let drawDateDisplay = item.draw_date || '未知';
                    if (item.draw_date) {
                        if (typeof item.draw_date === 'number') {
                            // 处理时间戳转为日期字符串
                            const date = new Date(item.draw_date);
                            drawDateDisplay = date.toLocaleString();
                        } else if (typeof item.draw_date === 'string') {
                            // 处理DATETIME字符串格式
                            const date = this.parseMySQLDateTime(item.draw_date);
                            if (date) {
                                drawDateDisplay = date.toLocaleString();
                            }
                        }
                    }
                    
                    // 获取期号
                    const periodDisplay = item.period_number || '未知';
                    
                    // 格式化开奖号码
                    let formattedNumbers = '未知';

                    // 优先使用独立的球号字段来格式化
                    if (item.lottery_type === 'double_ball' && item.red_balls && item.blue_ball) {
                        const numbersObj = {
                            redBalls: item.red_balls,
                            blueBall: item.blue_ball
                        };
                        formattedNumbers = this.formatDoubleBallNumbers(numbersObj);
                    } else if (item.lottery_type === 'super_lotto' && item.front_balls && item.back_balls) {
                        const numbersObj = {
                            frontBalls: item.front_balls,
                            backBalls: item.back_balls
                        };
                        formattedNumbers = this.formatSuperLottoNumbers(numbersObj);
                    } else if (item.lottery_type === 'arrange_five' && item.arrange_number) {
                        formattedNumbers = this.formatArrangeFiveNumbers(item.arrange_number);
                    } else if (item.winning_numbers) {
                        // 如果没有独立字段，尝试解析winning_numbers
                        if (item.lottery_type === 'double_ball') {
                            formattedNumbers = this.formatDoubleBallNumbers(item.winning_numbers);
                        } else if (item.lottery_type === 'super_lotto') {
                            formattedNumbers = this.formatSuperLottoNumbers(item.winning_numbers);
                        } else if (item.lottery_type === 'arrange_five') {
                            formattedNumbers = this.formatArrangeFiveNumbers(item.winning_numbers);
                        } else {
                            formattedNumbers = item.winning_numbers;
                        }
                    }
                    
                    let lotteryName = '未知彩票';
                    if (item.lottery_type === 'double_ball') {
                        lotteryName = '双色球';
                    } else if (item.lottery_type === 'super_lotto') {
                        lotteryName = '大乐透';
                    } else if (item.lottery_type === 'arrange_five') {
                        lotteryName = '排列5';
                    }

                    content = `
                        <h4>${lotteryName} 开奖</h4>
                        <p>期号: ${periodDisplay}</p>
                        <p>开奖号码: ${formattedNumbers}</p>
                        <p>开奖日期: ${drawDateDisplay}</p>
                        <p>中奖人数: ${item.total_winners || 0}人</p>
                    `;
                    renderedItems++;
                } else {
                    // 双色球和大乐透记录
                    try {
                        // 处理彩票号码字段
                        let numbersDisplay = '未知';
                        let numbers = null;
                        
                        if (typeof item.numbers === 'string') {
                            try {
                                numbers = JSON.parse(item.numbers);
                            } catch (e) {
                                // 解析失败，忽略错误
                                // 检查是否包含分隔符，可能是格式化后的字符串
                                if (item.numbers.includes('|')) {
                                    numbers = item.numbers;
                                } else if (type === 'arrange-five') {
                                    // 对于排列5，如果解析失败，可能是纯数字字符串
                                    numbers = { arrangeNumber: item.numbers };
                                }
                            }
                        } else if (typeof item.numbers === 'object') {
                            numbers = item.numbers;
                        }
                        
                        if (numbers) {
                            if (type === 'double-ball') {
                                numbersDisplay = this.formatDoubleBallNumbers(numbers);
                            } else if (type === 'super-lotto') {
                                numbersDisplay = this.formatSuperLottoNumbers(numbers);
                            } else if (type === 'arrange-five') {
                                numbersDisplay = this.formatArrangeFiveNumbers(numbers);
                            }
                        } else {
                            // 尝试使用独立的字段
                            if (type === 'double-ball' && (item.red_balls || item.redBalls)) {
                                const redBalls = item.red_balls || item.redBalls;
                                const blueBall = item.blue_ball || item.blueBall;
                                const numbersObj = { redBalls, blueBall };
                                numbersDisplay = this.formatDoubleBallNumbers(numbersObj);
                            } else if (type === 'super-lotto' && (item.front_balls || item.frontBalls)) {
                                const frontBalls = item.front_balls || item.frontBalls;
                                const backBalls = item.back_balls || item.backBalls;
                                const numbersObj = { frontBalls, backBalls };
                                numbersDisplay = this.formatSuperLottoNumbers(numbersObj);
                            } else if (type === 'arrange-five' && (item.arrange_number || item.arrangeNumber)) {
                                const arrangeNumber = item.arrange_number || item.arrangeNumber;
                                numbersDisplay = this.formatArrangeFiveNumbers(arrangeNumber);
                            }
                        }

                        // 对于排列5，如果仍然显示"未知"，再次尝试从arrange_number字段获取
                        if (type === 'arrange-five' && numbersDisplay === '未知' && (item.arrange_number || item.arrangeNumber)) {
                            const arrangeNumber = item.arrange_number || item.arrangeNumber;
                            numbersDisplay = this.formatArrangeFiveNumbers(arrangeNumber);
                        }
                        
                        // 格式化购买时间
                        let purchaseTimeDisplay = item.purchase_time || '未知';
                        if (item.purchase_time) {
                            if (typeof item.purchase_time === 'number') {
                                // 处理时间戳转为日期字符串
                                const date = new Date(item.purchase_time);
                                purchaseTimeDisplay = date.toLocaleString();
                            } else if (typeof item.purchase_time === 'string') {
                                // 处理DATETIME字符串格式
                                const date = this.parseMySQLDateTime(item.purchase_time);
                                if (date) {
                                    purchaseTimeDisplay = date.toLocaleString();
                                }
                            }
                        }
                        
                        // 格式化开奖时间
                        let drawDateDisplay = item.draw_date || '未知';
                        if (item.draw_date) {
                            if (typeof item.draw_date === 'number') {
                                // 处理时间戳转为日期字符串
                                const date = new Date(item.draw_date);
                                drawDateDisplay = date.toLocaleString();
                            } else if (typeof item.draw_date === 'string') {
                                // 处理DATETIME字符串格式
                                const date = this.parseMySQLDateTime(item.draw_date);
                                if (date) {
                                    drawDateDisplay = date.toLocaleString();
                                }
                            }
                        }
                        
                        // 获取期号
                        const periodNumber = item.draw_period || item.period_number || '未知';
                        
                        // 判断开奖状态
                        let status = '未知';
                        let statusClass = '';
                        const now = Date.now();
                        
                        // 处理draw_date，可能是时间戳或DATETIME字符串
                        let drawDate = 0;
                        if (item.draw_date) {
                            if (typeof item.draw_date === 'number') {
                                drawDate = item.draw_date;
                            } else if (typeof item.draw_date === 'string') {
                                // 尝试先解析为数字
                                const parsedDrawDate = parseInt(item.draw_date);
                                if (!isNaN(parsedDrawDate)) {
                                    drawDate = parsedDrawDate;
                                } else {
                                    // 尝试解析为DATETIME字符串
                                    const date = this.parseMySQLDateTime(item.draw_date);
                                    if (date) {
                                        drawDate = date.getTime();
                                    }
                                }
                            }
                        }
                        
                        // 处理purchase_time，可能是时间戳或DATETIME字符串
                        let purchaseTime = 0;
                        if (item.purchase_time) {
                            if (typeof item.purchase_time === 'number') {
                                purchaseTime = item.purchase_time;
                            } else if (typeof item.purchase_time === 'string') {
                                // 尝试先解析为数字
                                const parsedPurchaseTime = parseInt(item.purchase_time);
                                if (!isNaN(parsedPurchaseTime)) {
                                    purchaseTime = parsedPurchaseTime;
                                } else {
                                    // 尝试解析为DATETIME字符串
                                    const date = this.parseMySQLDateTime(item.purchase_time);
                                    if (date) {
                                        purchaseTime = date.getTime();
                                    }
                                }
                            }
                        }
                        
                        // 优先使用draw_period字段判断是否已开奖
                        if (item.draw_period) {
                            // 已经参与过开奖，根据is_winning判断结果
                            // 修复：确保正确比较is_winning值，无论它是数字还是字符串
                            const isWinning = item.is_winning === 1 || item.is_winning === '1' || item.is_winning === true;
                            status = isWinning ? '中奖' : '未中奖';
                            statusClass = isWinning ? 'winning-status' : 'losing-status';
                            
                            // 如果是中奖彩票，确保prize_amount字段有值
                            if (isWinning && !item.prize_amount && item.prize_level) {
                                // 尝试根据奖级推断奖金
                                let configKey = 'doubleBall';
                                if (item.lottery_type === 'super_lotto') {
                                    configKey = 'superLotto';
                                } else if (item.lottery_type === 'arrange_five') {
                                    configKey = 'arrangeFive';
                                }

                                const prizeConfig = this.config && this.config[configKey];
                                if (prizeConfig && prizeConfig.prizes && prizeConfig.prizes[item.prize_level]) {
                                    item.prize_amount = prizeConfig.prizes[item.prize_level].amount;
                                }
                            }
                        } else {
                            // 获取配置的开奖时间
                            let drawHour = 1;
                            let drawMinute = 36;

                            if (type === 'double-ball' && this.config && this.config.doubleBall) {
                                drawHour = this.config.doubleBall.drawTime.hour;
                                drawMinute = this.config.doubleBall.drawTime.minute;
                            } else if (type === 'super-lotto' && this.config && this.config.superLotto) {
                                drawHour = this.config.superLotto.drawTime.hour;
                                drawMinute = this.config.superLotto.drawTime.minute;
                            } else if (type === 'arrange-five' && this.config && this.config.arrangeFive) {
                                drawHour = this.config.arrangeFive.drawTime.hour;
                                drawMinute = this.config.arrangeFive.drawTime.minute;
                            }
                            
                            // 计算开奖时间
                            const drawTime = new Date(drawDate);
                            drawTime.setHours(drawHour, drawMinute, 0, 0); // 使用配置的开奖时间
                            const drawTimeTimestamp = drawTime.getTime();
                            
                            // 如果购买时间晚于开奖时间，需要进一步判断
                            if (purchaseTime > drawTimeTimestamp) {
                                // 获取下一次开奖时间（明天同一时间）
                                const nextDrawDate = drawTimeTimestamp + (24 * 60 * 60 * 1000);
                                
                                if (now < nextDrawDate) {
                                    status = '未开奖';
                                } else {
                                    // 已经过了下一期开奖时间，但服务器未更新状态
                                    status = '等待开奖结果';
                                }
                            } else if (drawTimeTimestamp > now) {
                                status = '未开奖';
                            } else {
                                // 已过开奖时间，但服务器未更新状态
                                status = '等待开奖结果';
                            }
                        }
                        
                        // 获取彩票类型名称
                        let lotteryTypeName = '未知彩票';
                        if (type === 'double-ball') {
                            lotteryTypeName = '双色球';
                        } else if (type === 'super-lotto') {
                            lotteryTypeName = '大乐透';
                        } else if (type === 'arrange-five') {
                            lotteryTypeName = '排列5';
                        }

                        content = `
                            <h4>${lotteryTypeName}</h4>
                            <p>号码: ${numbersDisplay}</p>
                            <p>期号: ${periodNumber}</p>
                            <p>购买时间: ${purchaseTimeDisplay}</p>
                            <p>开奖时间: ${drawDateDisplay}</p>
                            <p>状态: <span class="${statusClass}">${status}</span></p>
                            ${item.is_winning === 1 || item.is_winning === '1' || item.is_winning === true ?
                                (type === 'arrange-five' ?
                                    this.getArrangeFiveRewardDisplay(item) :
                                    `<p>奖金: ¥${this.formatNumber(item.prize_amount || 0)}</p>`
                                ) : ''}
                        `;
                        renderedItems++;
                    } catch (e) {
                        // 获取彩票类型名称
                        let lotteryTypeName = '未知彩票';
                        if (type === 'double-ball') {
                            lotteryTypeName = '双色球';
                        } else if (type === 'super-lotto') {
                            lotteryTypeName = '大乐透';
                        } else if (type === 'arrange-five') {
                            lotteryTypeName = '排列5';
                        }

                        content = `
                            <h4>${lotteryTypeName}</h4>
                            <p>购买时间: ${item.purchase_time || '未知'}</p>
                            <p>状态: 未知</p>
                        `;
                    }
                }
                
                historyItem.innerHTML = content;
                container.appendChild(historyItem);
            } catch (e) {
                // 渲染错误，忽略
            }
        });
        
        // 如果没有成功渲染任何记录，显示暂无记录
        if (renderedItems === 0) {
            container.innerHTML = '<p style="text-align: center; color: rgba(255,255,255,0.6);">暂无记录</p>';
        }
    }
    
    // 翻译刮刮乐类型名称
    translateCardType(cardType) {
        const types = {
            'scratch_xixiangfeng': '喜象丰彩',
            'scratch_fusong': '福鼠送彩',
            'scratch_yaocai': '耀出彩',
            'scratch_caizuan': '5倍彩钻',
            'scratch_zhongguofu': '中国福'
        };

        return types[cardType] || cardType;
    }
    
    // 加载未兑奖奖品
    loadUnclaimedPrizes() {
        fetch(`https://${this.GetParentResourceName()}/getUnclaimedPrizes`, {
            method: 'POST',
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            this.renderUnclaimedPrizes(data);
        })
        .catch(() => {});
    }
    
    // 渲染未兑奖奖品
    renderUnclaimedPrizes(data) {
        const container = document.getElementById('unclaimed-prizes');
        container.innerHTML = '';
        
        if (!data || data.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: rgba(255,255,255,0.6);">暂无未兑奖奖品</p>';
            return;
        }
        
        data.forEach(prize => {
            // 处理购买时间显示
            let purchaseTimeDisplay = prize.purchase_time || '未知';
            if (prize.purchase_time) {
                if (typeof prize.purchase_time === 'number') {
                    // 处理时间戳转为日期字符串
                    const date = new Date(prize.purchase_time);
                    purchaseTimeDisplay = date.toLocaleString();
                } else if (typeof prize.purchase_time === 'string') {
                    // 处理DATETIME字符串格式
                    const date = this.parseMySQLDateTime(prize.purchase_time);
                    if (date) {
                        purchaseTimeDisplay = date.toLocaleString();
                    }
                }
            }
            
            const prizeItem = document.createElement('div');
            prizeItem.className = 'prize-item';
            prizeItem.dataset.ticketId = prize.id; // 添加票ID到DOM元素
            prizeItem.innerHTML = `
                <div class="prize-info">
                    <h4>${prize.card_type ? this.translateCardType(prize.card_type) : this.getLotteryTypeName(prize.lottery_type)}</h4>
                    <p>购买时间: ${purchaseTimeDisplay}</p>
                </div>
                <div class="prize-amount">¥${this.formatNumber(prize.prize_amount)}</div>
                <button class="buy-btn" onclick="lotteryUI.claimPrize(${prize.id}, '${prize.card_type ? 'scratch' : 'lottery'}')">
                    兑奖
                </button>
            `;
            container.appendChild(prizeItem);
        });
    }
    
    // 处理更新未兑奖奖品
    updateUnclaimedPrizes(prizes) {
        this.renderUnclaimedPrizes(prizes);
    }
    
    // 兑奖
    claimPrize(ticketId, lotteryType) {
        // 添加锁定机制，防止重复点击
        if (this.claimingPrize) {
            return;
        }
        
        this.claimingPrize = true;
        this.showLoading(true);
        
        // 获取未兑奖项的中奖金额
        let prizeAmount = 0;
        const prizeElements = document.querySelectorAll('.prize-item');
        for (let i = 0; i < prizeElements.length; i++) {
            if (prizeElements[i].dataset.ticketId == ticketId) {
                const amountText = prizeElements[i].querySelector('.prize-amount').textContent;
                prizeAmount = parseInt(amountText.replace('¥', '').replace(/,/g, ''));
                break;
            }
        }
        
        // 先检查职业系统是否启用
        fetch(`https://${this.GetParentResourceName()}/checkLotteryJobEnabled`, {
            method: 'POST',
            body: JSON.stringify({ prizeAmount: prizeAmount })
        })
        .then(response => response.json())
        .then(data => {
            if (data.lotteryJobEnabled && data.prizeAmount >= 10000) {
                // 职业系统已启用且奖金金额大于等于10000，显示提示
                this.showLoading(false);
                this.claimingPrize = false;
                this.showMessage('请联系彩票店工作人员兑奖', 'info');
            } else {
                // 职业系统未启用或奖金金额小于10000，正常处理兑奖请求
                fetch(`https://${this.GetParentResourceName()}/claimPrize`, {
                    method: 'POST',
                    body: JSON.stringify({ ticketId, lotteryType })
                })
                .then(response => {
                    // 不要尝试解析JSON，直接返回空对象
                    // FiveM的NUI回调不返回实际数据，而是通过事件传递
                    return {};
                })
                .then(data => {
                    // 不在这里处理结果，等待caipiaoc:claimPrizeResult事件
                    this.showLoading(false);
                    this.claimingPrize = false;
                })
                .catch(() => {
                    this.showLoading(false);
                    this.claimingPrize = false;
                    this.showMessage('兑奖失败: 网络错误', 'error');
                });
            }
        })
        .catch(() => {
            // 检查失败，默认使用正常流程
            this.showLoading(false);
            this.claimingPrize = false;
            this.showMessage('检查配置失败', 'error');
        });
    }
    
    // 显示公告 - 改为使用toast通知，与开奖结果保持一致
    showAnnouncement(message, type) {
        // 使用showMessage函数显示toast通知，而不是模态弹窗
        let notificationType = 'info';
        let title = '中奖公告';

        // 根据类型设置不同的通知样式和标题
        if (type === 'win' || type === 'jackpot') {
            notificationType = 'success';
            title = '🎉 恭喜中奖';
        } else if (type === 'error') {
            notificationType = 'error';
            title = '⚠️ 提示';
        } else if (type === 'draw') {
            notificationType = 'info';
            title = '📢 开奖公告';
        }

        // 使用toast通知显示中奖公告
        this.showMessage(message, notificationType, title);

        // 播放相应的音效
        if (type === 'win' || type === 'jackpot') {
            this.playSound('win');
        } else if (type === 'error') {
            this.playSound('alert');
        }

        // 不再设置isAnnouncementOpen，因为不再使用模态弹窗
        this.isAnnouncementOpen = false;
    }
    
    // 显示开奖结果
    // 注意：此函数不再被使用，开奖结果现在只在聊天窗口全服广播
    // 保留此函数以便将来可能需要恢复弹窗功能
    showDrawResult(drawData) {
        if (!drawData) {
            return;
        }
        
        let message = '';
        
        // 格式化中奖号码显示
        if (drawData.lotteryType === 'double_ball') {
            message = `双色球开奖结果: ${drawData.winningNumbers.formatted}`;
        } else if (drawData.lotteryType === 'super_lotto') {
            message = `大乐透开奖结果: ${drawData.winningNumbers.formatted}`;
        } else {
            message = `${drawData.lotteryType}开奖结果: ${drawData.winningNumbers.formatted}`;
        }
        
        // 如果有中奖人数信息，添加到消息中
        if (drawData.totalWinners !== undefined) {
            message += `\n共有 ${drawData.totalWinners} 人中奖`;
        }
        
        this.showAnnouncement(message, 'draw');
    }
    
    // 显示消息
    showMessage(message, type = 'info', title = '') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        // 添加标题（如果有）
        let content = '';
        if (title) {
            content += `<div class="toast-title">${title}</div>`;
        }
        content += `<div class="toast-message">${message}</div>`;
        
        toast.innerHTML = content;
        
        // 添加到body
        document.body.appendChild(toast);
        
        // 动画显示
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // 3秒后移除
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
        
        // 播放提示音
        this.playSound(type === 'success' ? 'success' : 'alert');
    }
    
    // 显示确认对话框
    showConfirm(message, callback) {
        const modal = document.getElementById('confirm-modal');
        const text = document.getElementById('confirm-text');
        
        text.textContent = message;
        modal.classList.remove('hidden');
        this.confirmCallback = callback;
    }
    
    // 显示/隐藏加载动画
    showLoading(show) {
        const loading = document.getElementById('loading');
        if (show) {
            loading.classList.remove('hidden');
        } else {
            loading.classList.add('hidden');
        }
    }
    
    // 播放音效
    playSound(soundName) {
        if (!this.config || !this.config.ui || !this.config.ui.soundEffects) return;
        
        const audio = document.getElementById(`${soundName}-sound`);
        if (audio) {
            audio.currentTime = 0;
            audio.play().catch(() => {
                // 忽略音频播放错误
            });
        }
    }
    
    // 格式化数字
    formatNumber(num) {
        return new Intl.NumberFormat('zh-CN').format(num);
    }
    
    // 获取资源名称
    GetParentResourceName() {
        return window.GetParentResourceName ? window.GetParentResourceName() : 'zh_lottery';
    }
    
    // 设置福鼠送彩游戏
    setupFuSongGame(ticketData, container) {
        container.innerHTML = '';
        
        // 第一部分：只显示中奖号码数字，不显示文字标题
        const winningNumberContainer = document.createElement('div');
        winningNumberContainer.className = 'fusong-winning-number-container';
        
        // 使用服务器传递的中奖号码，不再随机选择
        const winningNumber = ticketData.winningNumber;
        
        winningNumberContainer.innerHTML = `
            <div class="fusong-winning-number">${winningNumber}</div>
        `;
        container.appendChild(winningNumberContainer);
        
        // 第二部分：5×5网格，纯数字显示
        const gridContainer = document.createElement('div');
        gridContainer.className = 'fusong-grid-container';
        
        // 生成5行5列的表格，每行最后添加一个金额
        // 使用服务器传递的行金额，如果没有则使用默认值
        const amountValues = ticketData.rowAmounts || [10, 20, 50, 100, 500]; // 使用服务器配置的行金额
        
        let gridHTML = '<table class="fusong-grid">';
        
        // 使用服务器传递的号码，不再生成随机号码
        const numbers = ticketData.myNumbers;
        
        let numberIndex = 0;
        for (let row = 0; row < 5; row++) { // 从6行改为5行
            gridHTML += '<tr>';
            // 添加5个数字单元格
            for (let col = 0; col < 5; col++) {
                const currentNum = numbers[numberIndex];
                const isWinning = currentNum === winningNumber;
                gridHTML += `<td class="fusong-number-cell" data-index="${numberIndex}" data-winning="${isWinning}">${currentNum}</td>`;
                numberIndex++;
            }
            // 添加1个金额单元格，使用行对应的金额值
            gridHTML += `<td class="fusong-amount-cell">¥${amountValues[row]}</td>`;
            gridHTML += '</tr>';
        }
        
        gridHTML += '</table>';
        gridContainer.innerHTML = gridHTML;
        container.appendChild(gridContainer);
        
        // 更新刮刮乐类型
        container.classList.add('fusong');
    }
    
    // 设置耀出彩游戏
    setupYaoCaiGame(ticketData, container) {
        container.innerHTML = '';
        container.style.gridTemplateColumns = 'repeat(5, 1fr)';
        
        // 创建中奖号码容器，可独立定位
        const winningNumbersContainer = document.createElement('div');
        winningNumbersContainer.className = 'winning-numbers-container';
        
        const winningNumbersDiv = document.createElement('div');
        winningNumbersDiv.className = 'winning-numbers';
        winningNumbersDiv.innerHTML = `
                ${ticketData.winningNumbers.join('&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;')}
        `;
        winningNumbersContainer.appendChild(winningNumbersDiv);
        container.appendChild(winningNumbersContainer);
        
        // 创建号码网格容器，可独立定位
        const numbersGridContainer = document.createElement('div');
        numbersGridContainer.className = 'numbers-grid-container';
        
        const numberGrid = document.createElement('div');
        numberGrid.className = 'number-grid';
        numberGrid.innerHTML = `
            ${ticketData.myNumbers.map((num, index) => {
                // 使用服务器提供的金额，如果没有则使用默认值
                const amount = ticketData.numberAmounts && ticketData.numberAmounts[index] 
                    ? ticketData.numberAmounts[index] 
                    : this.getRandomAmount(num);
                return `
                    <div class="number-cell" data-index="${index}">
                        <div class="cell-number">${num}</div>
                        <div class="cell-amount">¥${amount}</div>
            </div>
        `;
            }).join('')}
        `;
        numbersGridContainer.appendChild(numberGrid);
        container.appendChild(numbersGridContainer);
    }

    // 设置5倍彩钻游戏
    setupCaiZuanGame(ticketData, container) {
        container.innerHTML = '';
        container.style.gridTemplateColumns = 'repeat(10, 1fr)';

        // 创建中奖号码容器，可独立定位
        const winningNumbersContainer = document.createElement('div');
        winningNumbersContainer.className = 'winning-numbers-container';

        // 创建中奖号码网格，4行5列显示20个中奖号码
        const winningNumbersGrid = document.createElement('div');
        winningNumbersGrid.className = 'winning-numbers-grid';

        // 生成中奖号码网格HTML
        let winningGridHTML = '';
        for (let i = 0; i < ticketData.winningNumbers.length; i++) {
            const number = ticketData.winningNumbers[i];
            // 优先使用显示名称，如果没有则使用原始物品名称
            const item = ticketData.winningNumberItemsDisplay && ticketData.winningNumberItemsDisplay[i]
                ? ticketData.winningNumberItemsDisplay[i]
                : (ticketData.winningNumberItems && ticketData.winningNumberItems[i]
                    ? ticketData.winningNumberItems[i]
                    : '面包');

            // 检查是否是钻石图案位置
            const isDiamondPosition = ticketData.diamondPositions && ticketData.diamondPositions.includes(i + 1);
            const symbolClass = isDiamondPosition ? 'diamond-symbol' : 'normal-symbol';
            const symbolText = isDiamondPosition ? '💎' : number;

            winningGridHTML += `
                <div class="winning-number-cell ${symbolClass}" data-index="${i}">
                    <div class="cell-symbol">${symbolText}</div>
                    <div class="cell-item">${item}</div>
                </div>
            `;
        }

        winningNumbersGrid.innerHTML = winningGridHTML;
        winningNumbersContainer.appendChild(winningNumbersGrid);
        container.appendChild(winningNumbersContainer);

        // 创建我的号码容器，可独立定位
        const myNumbersContainer = document.createElement('div');
        myNumbersContainer.className = 'my-numbers-container';

        const myNumbersGrid = document.createElement('div');
        myNumbersGrid.className = 'my-numbers-grid';
        myNumbersGrid.innerHTML = `
            ${ticketData.myNumbers.map((num, index) => {
                // 优先使用显示名称，如果没有则使用原始物品名称
                const item = ticketData.numberItemsDisplay && ticketData.numberItemsDisplay[index]
                    ? ticketData.numberItemsDisplay[index]
                    : (ticketData.numberItems && ticketData.numberItems[index]
                        ? ticketData.numberItems[index]
                        : '面包');
                return `
                    <div class="my-number-cell" data-index="${index}">
                        <div class="cell-number">${num}</div>
                        <div class="cell-item">${item}</div>
                    </div>
                `;
            }).join('')}
        `;
        myNumbersContainer.appendChild(myNumbersGrid);
        container.appendChild(myNumbersContainer);
    }

    // 设置中国福游戏
    setupZhongGuoFuGame(ticketData, container) {
        container.innerHTML = '';
        container.style.gridTemplateColumns = 'repeat(10, 1fr)';

        // 创建中奖号码容器，可独立定位
        const winningNumbersContainer = document.createElement('div');
        winningNumbersContainer.className = 'winning-numbers-container';

        // 创建中奖号码网格，3行5列显示15个中奖号码
        const winningNumbersGrid = document.createElement('div');
        winningNumbersGrid.className = 'zhongguofu-winning-numbers-grid';

        // 生成中奖号码网格HTML
        let winningGridHTML = '';
        for (let i = 0; i < ticketData.winningNumbers.length; i++) {
            const number = ticketData.winningNumbers[i];
            // 优先使用显示名称，如果没有则使用原始物品名称
            const item = ticketData.winningNumberItemsDisplay && ticketData.winningNumberItemsDisplay[i]
                ? ticketData.winningNumberItemsDisplay[i]
                : (ticketData.winningNumberItems && ticketData.winningNumberItems[i]
                    ? ticketData.winningNumberItems[i]
                    : '');

            // 检查是否是福符号位置
            const isFuPosition = ticketData.fuPositions && ticketData.fuPositions.includes(i + 1);
            const cellClass = isFuPosition ? 'zhongguofu-winning-number-cell fu-symbol' : 'zhongguofu-winning-number-cell';

            winningGridHTML += `
                <div class="${cellClass}" data-index="${i}">
                    ${isFuPosition ?
                        '<div class="cell-symbol">福</div>' :
                        `<div class="cell-number">${number}</div>`
                    }
                    <div class="cell-item">${item}</div>
                </div>
            `;
        }

        winningNumbersGrid.innerHTML = winningGridHTML;
        winningNumbersContainer.appendChild(winningNumbersGrid);
        container.appendChild(winningNumbersContainer);

        // 创建我的号码容器，可独立定位
        const myNumbersContainer = document.createElement('div');
        myNumbersContainer.className = 'my-numbers-container';

        // 创建我的号码网格，1行2列显示2个我的号码
        const myNumbersGrid = document.createElement('div');
        myNumbersGrid.className = 'zhongguofu-my-numbers-grid';

        // 生成我的号码网格HTML
        myNumbersGrid.innerHTML = `
            ${ticketData.myNumbers.map((num, index) => {
                const item = ticketData.numberItems && ticketData.numberItems[index] ? ticketData.numberItems[index] : '';
                return `
                    <div class="zhongguofu-my-number-cell" data-index="${index}">
                        <div class="cell-number">${num}</div>
                        <div class="cell-item">${item}</div>
                    </div>
                `;
            }).join('')}
        `;
        myNumbersContainer.appendChild(myNumbersGrid);
        container.appendChild(myNumbersContainer);
    }

    // 设置乘风破浪游戏
    setupChengFengGame(ticketData, container) {
        container.innerHTML = '';
        container.style.gridTemplateColumns = 'repeat(5, 1fr)';

        // 创建图标网格容器，4行5列显示20个图标
        const iconsGridContainer = document.createElement('div');
        iconsGridContainer.className = 'chengfeng-icons-grid-container';

        const iconsGrid = document.createElement('div');
        iconsGrid.className = 'chengfeng-icons-grid';

        // 生成图标网格HTML
        let iconsGridHTML = '';
        for (let i = 0; i < 20; i++) {
            const iconType = ticketData.icons[i];
            // 优先使用显示名称，如果没有则使用原始物品名称
            const item = ticketData.iconItemsDisplay && ticketData.iconItemsDisplay[i]
                ? ticketData.iconItemsDisplay[i]
                : (ticketData.iconItems && ticketData.iconItems[i]
                    ? ticketData.iconItems[i]
                    : '');

            let cellClass = 'chengfeng-icon-cell';
            let iconDisplay = '';

            if (iconType === 'sail') {
                cellClass += ' sail-icon';
                iconDisplay = '<div class="cell-icon">⛵</div>';
            } else if (iconType === '🌊') {
                cellClass += ' tornado-icon';
                iconDisplay = '<div class="cell-icon">🌊</div>';
            } else {
                cellClass += ' normal-icon';
                // 直接显示后端传来的图标，支持多样化的普通图标
                iconDisplay = `<div class="cell-icon">${iconType}</div>`;
            }

            iconsGridHTML += `
                <div class="${cellClass}" data-index="${i}">
                    ${iconDisplay}
                    <div class="cell-item">${item}</div>
                </div>
            `;
        }

        iconsGrid.innerHTML = iconsGridHTML;
        iconsGridContainer.appendChild(iconsGrid);
        container.appendChild(iconsGridContainer);
    }

    // 获取随机奖金金额，用于耀出彩的显示 (作为后备方案)
    getRandomAmount(number) {
        // 基于号码生成一个固定的模拟金额，保证每次相同号码显示相同金额
        const baseAmounts = [10, 20, 50, 100, 200, 500, 1000, 2000, 5000, 10000];
        const index = number % baseAmounts.length;
        return baseAmounts[index];
    }
    
    // 处理福鼠送彩刮奖
    handleFuSongScratch(cellIndex, numberValue, isWinning) {
        // 发送刮奖结果到客户端
        fetch(`https://${this.GetParentResourceName()}/scratchArea`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                areaIndex: cellIndex,
                result: {
                    number: numberValue,
                    isWinning: isWinning
                }
            })
        });
        
        // 更新UI
        const cell = document.querySelector(`.fusong-number-cell[data-index="${cellIndex}"]`);
        if (cell) {
            cell.classList.add('scratched');
            if (isWinning) {
                cell.classList.add('winning');
            }
        }
        
        // 检查刮开区域百分比
        this.checkScratchPercentage();
    }
    
    // 处理耀出彩刮奖
    handleYaoCaiScratch(areaIndex) {
        const myNumber = this.scratchData.ticketData.myNumbers[areaIndex];
        const isWinning = this.scratchData.ticketData.winningNumbers.includes(myNumber);

        // 发送刮奖结果到客户端
        fetch(`https://${this.GetParentResourceName()}/scratchArea`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                areaIndex: areaIndex,
                result: {
                    number: myNumber,
                    isWinning: isWinning
                }
            })
        });

        // 更新UI
        const numberCell = document.querySelector(`.number-cell[data-index="${areaIndex}"]`);
        if (numberCell) {
            numberCell.classList.add('scratched');
            if (isWinning) {
                numberCell.classList.add('winning');
            }
        }

        // 检查刮开区域百分比
        this.checkScratchPercentage();
    }

    // 处理5倍彩钻刮奖
    handleCaiZuanScratch(areaIndex) {
        const myNumber = this.scratchData.ticketData.myNumbers[areaIndex];
        const isWinning = this.scratchData.ticketData.winningNumbers.includes(myNumber);

        // 发送刮奖结果到客户端
        fetch(`https://${this.GetParentResourceName()}/scratchArea`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                areaIndex: areaIndex,
                result: {
                    number: myNumber,
                    isWinning: isWinning
                }
            })
        });

        // 更新UI
        const numberCell = document.querySelector(`.my-number-cell[data-index="${areaIndex}"]`);
        if (numberCell) {
            numberCell.classList.add('scratched');
            if (isWinning) {
                numberCell.classList.add('winning');
            }
        }

        // 检查刮开区域百分比
        this.checkScratchPercentage();
    }
    
    // 初始化事件监听
    initEventListeners() {
        // ... existing code ...
        
        // 福鼠送彩刮奖事件
        document.addEventListener('click', (e) => {
            if (!this.scratchData) return;
            
            if (this.scratchData.cardType === 'scratch_fusong') {
                const numberCell = e.target.closest('.fusong-number-cell');
                if (numberCell) {
                    const cellIndex = parseInt(numberCell.dataset.index);
                    const isWinning = numberCell.dataset.winning === 'true';
                    const numberValue = numberCell.textContent;
                    
                    if (!numberCell.classList.contains('scratched')) {
                        this.handleFuSongScratch(cellIndex, numberValue, isWinning);
                    }
                }
            } else if (this.scratchData.cardType === 'scratch_yaocai') {
                const numberCell = e.target.closest('.number-cell');
                if (numberCell) {
                    const areaIndex = parseInt(numberCell.dataset.index);
                    this.handleYaoCaiScratch(areaIndex);
                }
            }
        });
    }
    
    // 处理购买结果
    handleBuyResult(result) {
        this.showLoading(false);
        if (result.success) {
            this.showMessage('购买成功！', 'success');
            
            // 清除已选号码
            if (result.ticketId) {
                // 根据当前打开的标签决定清除哪种彩票选择
                const activeTab = document.querySelector('.nav-btn.active');
                if (activeTab) {
                    const tabName = activeTab.dataset.tab;
                    if (tabName === 'double-ball') {
                        this.clearDoubleBall();
                        // 立即加载双色球记录
                        this.loadSpecificHistory('double-ball');
                    } else if (tabName === 'super-lotto') {
                        this.clearSuperLotto();
                        // 立即加载大乐透记录
                        this.loadSpecificHistory('super-lotto');
                    } else if (tabName === 'arrange-five') {
                        this.clearArrangeFive();
                        // 立即加载排列5记录
                        this.loadSpecificHistory('arrange-five');
                    }
                }
                
                // 加载奖池数据
                this.loadPrizePools();
                
                // 如果购票记录页面是打开的，刷新记录
                const activeHistoryTab = document.querySelector('.history-nav-btn.active');
                if (activeHistoryTab) {
                    const historyType = activeHistoryTab.dataset.history;
                    if (historyType === 'double-ball' || historyType === 'super-lotto' || historyType === 'arrange-five') {
                        // 延迟短暂时间确保缓存已更新
                        setTimeout(() => {
                            this.switchHistoryTab(historyType);
                        }, 100);
                    }
                }
            }
        } else {
            this.showMessage(result.message || '购买失败', 'error');
        }
    }
    
    // 添加一个辅助函数来解析MySQL DATETIME格式的字符串
    parseMySQLDateTime(dateTimeStr) {
        // 检查是否是有效的字符串
        if (!dateTimeStr || typeof dateTimeStr !== 'string') {
            return null;
        }
        
        // 尝试解析MySQL DATETIME格式: YYYY-MM-DD HH:MM:SS
        const pattern = /^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/;
        const matches = dateTimeStr.match(pattern);
        
        if (matches) {
            const year = parseInt(matches[1]);
            const month = parseInt(matches[2]) - 1; // JavaScript月份从0开始
            const day = parseInt(matches[3]);
            const hour = parseInt(matches[4]);
            const minute = parseInt(matches[5]);
            const second = parseInt(matches[6]);
            
            return new Date(year, month, day, hour, minute, second);
        }
        
        return null;
    }
    
    // 处理开奖历史数据
    handleDrawHistoryData(data) {
        // 确保data是数组
        if (!Array.isArray(data)) {
            data = Array.isArray(data) ? data : (data ? [data] : []);
        }

        if (data.length > 0) {
            // 确保每条记录都有必要的字段
            data.forEach((item, index) => {
                // 处理winning_numbers字段
                if (item.winning_numbers) {
                    // 如果winning_numbers是JSON字符串，尝试解析
                    if (typeof item.winning_numbers === 'string' &&
                        (item.winning_numbers.startsWith('{') || item.winning_numbers.startsWith('['))) {
                        try {
                            const parsedNumbers = JSON.parse(item.winning_numbers);

                            // 根据彩票类型处理解析后的数据
                            if (item.lottery_type === 'double_ball' && parsedNumbers.redBalls && parsedNumbers.blueBall) {
                                // 双色球：构建格式化字符串
                                const redBalls = Array.isArray(parsedNumbers.redBalls) ?
                                    parsedNumbers.redBalls : [parsedNumbers.redBalls];
                                item.winning_numbers = redBalls.join(', ') + ' | ' + parsedNumbers.blueBall;

                                // 同时设置独立字段供格式化函数使用
                                item.red_balls = redBalls;
                                item.blue_ball = parsedNumbers.blueBall;
                            } else if (item.lottery_type === 'super_lotto' && parsedNumbers.frontBalls && parsedNumbers.backBalls) {
                                // 大乐透：构建格式化字符串
                                const frontBalls = Array.isArray(parsedNumbers.frontBalls) ?
                                    parsedNumbers.frontBalls : [parsedNumbers.frontBalls];
                                const backBalls = Array.isArray(parsedNumbers.backBalls) ?
                                    parsedNumbers.backBalls : [parsedNumbers.backBalls];
                                item.winning_numbers = frontBalls.join(', ') + ' | ' + backBalls.join(', ');

                                // 同时设置独立字段供格式化函数使用
                                item.front_balls = frontBalls;
                                item.back_balls = backBalls;
                            } else if (item.lottery_type === 'arrange_five' && parsedNumbers.arrangeNumber) {
                                // 排列5：直接使用5位数字
                                item.winning_numbers = parsedNumbers.arrangeNumber.toString().padStart(5, '0');
                                item.arrange_number = parsedNumbers.arrangeNumber;
                            } else if (parsedNumbers.formatted) {
                                // 如果有formatted字段，直接使用
                                item.winning_numbers = parsedNumbers.formatted;
                            }
                        } catch (e) {
                            console.warn('解析winning_numbers JSON失败:', e, item.winning_numbers);
                        }
                    }
                } else {
                    // 如果winning_numbers为空，尝试从其他字段构建
                    if (item.red_balls && item.blue_ball) {
                        try {
                            const redBalls = typeof item.red_balls === 'string' ?
                                JSON.parse(item.red_balls) : item.red_balls;

                            if (Array.isArray(redBalls)) {
                                item.winning_numbers = redBalls.join(', ') + ' | ' + item.blue_ball;
                            }
                        } catch (e) {
                            // 解析失败，忽略错误
                        }
                    } else if (item.front_balls && item.back_balls) {
                        try {
                            const frontBalls = typeof item.front_balls === 'string' ?
                                JSON.parse(item.front_balls) : item.front_balls;
                            const backBalls = typeof item.back_balls === 'string' ?
                                JSON.parse(item.back_balls) : item.back_balls;

                            if (Array.isArray(frontBalls) && Array.isArray(backBalls)) {
                                item.winning_numbers = frontBalls.join(', ') + ' | ' + backBalls.join(', ');
                            }
                        } catch (e) {
                            // 解析失败，忽略错误
                        }
                    }
                    // 如果还是没有，设置一个默认值
                    if (!item.winning_numbers) {
                        item.winning_numbers = '数据错误';
                    }
                }
                
                // 确保period_number字段存在
                if (!item.period_number) {
                    item.period_number = '未知期号';
                }
                
                // 确保total_winners字段存在
                if (item.total_winners === undefined) {
                    item.total_winners = 0;
                }
            });
        }
        
        // 如果是通过receiveDrawHistory事件接收的数据，合并到现有数据中
        if (this.drawHistoryData && Array.isArray(this.drawHistoryData)) {
            // 检查是否已经有相同ID的记录，避免重复
            const existingIds = this.drawHistoryData.map(item => item.id);
            const newData = data.filter(item => !existingIds.includes(item.id));
            
            // 合并新数据
            this.drawHistoryData = this.drawHistoryData.concat(newData);
            
            // 使用合并后的数据进行渲染
            data = this.drawHistoryData;
        }
        
        // 按开奖日期降序排序，确保最新的记录显示在最上面
        data.sort((a, b) => {
            // 获取日期对象
            let dateA, dateB;
            
            // 处理a的日期
            if (typeof a.draw_date === 'number') {
                dateA = new Date(a.draw_date);
            } else if (typeof a.draw_date === 'string') {
                // 尝试解析为日期
                dateA = this.parseMySQLDateTime(a.draw_date) || new Date(a.draw_date);
            } else {
                dateA = new Date(0); // 默认最早日期
            }
            
            // 处理b的日期
            if (typeof b.draw_date === 'number') {
                dateB = new Date(b.draw_date);
            } else if (typeof b.draw_date === 'string') {
                // 尝试解析为日期
                dateB = this.parseMySQLDateTime(b.draw_date) || new Date(b.draw_date);
            } else {
                dateB = new Date(0); // 默认最早日期
            }
            
            // 降序排序（最新的在前）
            return dateB - dateA;
        });
        
        // 渲染开奖历史列表
        const container = document.getElementById('draw-history-list');
        if (container) {
            this.renderHistoryList(data, 'draw-history-list', 'draw');
        }
    }
    
    // 刮奖动作
    scratch(e) {
        const rect = this.scratchCanvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // 移除喜相逢类型的区域限制，所有类型都使用相同的刮除逻辑
        const radius = 24;
        this.scratchContext.beginPath();
        this.scratchContext.arc(x, y, radius, 0, 2 * Math.PI);
        this.scratchContext.fill();
        this.playSound('scratch');
        
        // 使用防抖函数检查刮开百分比，避免频繁检查
        if (this.scratchCheckTimeout) {
            clearTimeout(this.scratchCheckTimeout);
        }
        this.scratchCheckTimeout = setTimeout(() => {
            this.checkScratchPercentage();
        }, 300); // 300毫秒后检查
    }
}

// 初始化UI
const lotteryUI = new LotteryUI();

// 调试代码：添加全局消息监听器
window.addEventListener('message', function(event) {
    const data = event.data;
    if (!data.action) return;
    
    if (data.action === 'updatePlayerTickets') {
        // 处理彩票记录更新
    } else if (data.action === 'receiveDrawHistory') {
        // 处理开奖历史数据
    } else if (data.action === 'openAdminUI') {
        // 创建iframe加载admin.html
        const adminIframe = document.createElement('iframe');
        adminIframe.id = 'admin-iframe';
        adminIframe.style.position = 'absolute';
        adminIframe.style.top = '0';
        adminIframe.style.left = '0';
        adminIframe.style.width = '100%';
        adminIframe.style.height = '100%';
        adminIframe.style.border = 'none';
        adminIframe.style.zIndex = '1000';
        adminIframe.src = 'admin.html';
        
        // 添加到body
        document.body.appendChild(adminIframe);

        // 监听iframe加载完成事件
        adminIframe.onload = function() {
            if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                console.log("admin iframe已加载完成");
            }

            // 检查是否需要显示配置页面
            if (window.showConfigAfterAdminOpen) {
                if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                    console.log("检测到需要显示配置页面的标记，延迟1秒后显示");
                }
                setTimeout(() => {
                    adminIframe.contentWindow.postMessage({
                        action: 'showConfigPage'
                    }, '*');
                    window.showConfigAfterAdminOpen = false; // 清除标记
                }, 1000);
            }
        };

        // 监听来自iframe的消息
        window.addEventListener('message', function(iframeEvent) {
            const iframeData = iframeEvent.data;
            if (!iframeData || !iframeData.action) return;
            
            // 处理来自iframe的请求
            if (iframeData.action === 'requestAdminData') {
                // 转发请求到服务器
                fetch(`https://${lotteryUI.GetParentResourceName()}/getAdminData`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
            } else if (iframeData.action === 'closeAdminSystem') {
                // 关闭iframe
                const iframe = document.getElementById('admin-iframe');
                if (iframe) {
                    document.body.removeChild(iframe);
                }
                
                // 通知服务器关闭管理系统
                fetch(`https://${lotteryUI.GetParentResourceName()}/closeAdminSystem`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
            }
        });
    } else if (data.action === 'updateUnclaimedPrizes') {
        // 处理未兑奖奖品更新
    } else if (data.action === 'receiveAdminData') {
        // 转发管理系统数据到iframe
        const iframe = document.getElementById('admin-iframe');
        if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage({
                action: 'openAdminSystem',
                data: data.data,
                currentTab: data.currentTab || 'sales'
            }, '*');
        }
    } else if (data.action === 'receiveLotteryConfig') {
        // 转发彩票配置数据到iframe
        if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
            console.log("主页面收到彩票配置数据，转发到iframe:", data);
        }
        const iframe = document.getElementById('admin-iframe');
        if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage({
                action: 'receiveLotteryConfig',
                config: data.config
            }, '*');
            if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                console.log("彩票配置数据已转发到iframe");
            }
        } else {
            if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                console.error("找不到admin iframe或iframe未加载完成");
            }
        }
    } else if (data.action === 'saveLotteryConfigResult') {
        // 转发彩票配置保存结果到iframe
        if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
            console.log("主页面收到彩票配置保存结果，转发到iframe:", data);
        }
        const iframe = document.getElementById('admin-iframe');
        if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage(data, '*');
            if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                console.log("彩票配置保存结果已转发到iframe");
            }
        }
    } else if (data.action === 'saveScratchConfigResult') {
        // 转发刮刮乐配置保存结果到iframe
        if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
            console.log("主页面收到刮刮乐配置保存结果，转发到iframe:", data);
        }
        const iframe = document.getElementById('admin-iframe');
        if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage(data, '*');
            if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                console.log("刮刮乐配置保存结果已转发到iframe");
            }
        }
    } else if (data.action === 'saveScratchRatesResult') {
        // 转发刮刮乐权重配置保存结果到iframe
        if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
            console.log("主页面收到刮刮乐权重配置保存结果，转发到iframe:", data);
        }
        const iframe = document.getElementById('admin-iframe');
        if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage(data, '*');
            if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                console.log("刮刮乐权重配置保存结果已转发到iframe");
            }
        }
    } else if (data.action === 'showConfigPage') {
        // 处理显示彩票配置页面请求
        if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
            console.log("主页面收到显示彩票配置页面请求:", data);
        }
        const iframe = document.getElementById('admin-iframe');
        if (iframe && iframe.contentWindow) {
            // 如果iframe存在，直接转发消息
            iframe.contentWindow.postMessage(data, '*');
            if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                console.log("显示彩票配置页面请求已转发到iframe");
            }
        } else {
            // 如果iframe不存在，说明管理系统还没打开，先等待iframe创建
            if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                console.log("admin iframe未找到，等待管理系统打开...");
            }

            // 设置一个检查器，等待iframe加载完成
            let checkCount = 0;
            const maxChecks = 20; // 最多检查20次（10秒）
            const checkInterval = setInterval(() => {
                checkCount++;
                const iframe = document.getElementById('admin-iframe');

                if (iframe && iframe.contentWindow) {
                    // iframe已加载，转发消息
                    clearInterval(checkInterval);
                    iframe.contentWindow.postMessage(data, '*');
                    if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                        console.log("延迟转发：显示彩票配置页面请求已转发到iframe");
                    }
                } else if (checkCount >= maxChecks) {
                    // 超时，停止检查
                    clearInterval(checkInterval);
                    if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
                        console.error("等待admin iframe超时，无法显示彩票配置页面");
                    }
                }
            }, 500); // 每500ms检查一次
        }
    } else if (data.action === 'setShowConfigFlag') {
        // 设置显示配置页面的标记
        if (window.lotteryUI && window.lotteryUI.config && window.lotteryUI.config.debug) {
            console.log("设置显示配置页面标记");
        }
        window.showConfigAfterAdminOpen = true;
    }
});

// 排列5相关方法
LotteryUI.prototype.updateArrangeFiveNumber = function(value) {
    // 只允许数字输入
    value = value.replace(/[^0-9]/g, '');

    // 限制最多5位
    if (value.length > 5) {
        value = value.substring(0, 5);
    }

    // 补零到5位
    const paddedValue = value.padEnd(5, '0');
    this.selectedNumbers.arrangeFive = paddedValue;

    // 更新输入框显示
    document.getElementById('arrange-five-number').value = value;

    // 更新数字显示
    this.updateArrangeFiveDisplay();
};

LotteryUI.prototype.updateArrangeFiveDisplay = function() {
    const digits = document.querySelectorAll('.number-digit');
    const number = this.selectedNumbers.arrangeFive;

    for (let i = 0; i < 5; i++) {
        if (digits[i]) {
            digits[i].textContent = number[i] || '0';
            // 添加高亮效果
            if (number[i] && number[i] !== '0') {
                digits[i].classList.add('highlight');
            } else {
                digits[i].classList.remove('highlight');
            }
        }
    }
};

LotteryUI.prototype.randomArrangeFive = function() {
    // 生成随机5位数
    const randomNumber = Math.floor(Math.random() * 100000).toString().padStart(5, '0');
    this.selectedNumbers.arrangeFive = randomNumber;

    // 更新输入框
    document.getElementById('arrange-five-number').value = randomNumber;

    // 更新显示
    this.updateArrangeFiveDisplay();

    this.showMessage('已为您随机选择号码: ' + randomNumber, 'success');
};

LotteryUI.prototype.clearArrangeFive = function() {
    this.selectedNumbers.arrangeFive = '00000';
    document.getElementById('arrange-five-number').value = '';
    this.updateArrangeFiveDisplay();
    this.showMessage('已清空选择', 'info');
};

LotteryUI.prototype.buyArrangeFive = function() {
    const number = this.selectedNumbers.arrangeFive;

    // 验证号码
    if (!number || number === '00000') {
        this.showMessage('请选择您的5位数号码', 'warning');
        return;
    }

    this.showLoading(true);

    fetch(`https://${this.GetParentResourceName()}/buyArrangeFive`, {
        method: 'POST',
        body: JSON.stringify({
            arrangeNumber: number
        })
    })
    .then(response => response.json())
    .catch(() => {
        this.showLoading(false);
    });
};

LotteryUI.prototype.formatArrangeFiveNumbers = function(numbers) {
    if (typeof numbers === 'string') {
        // 如果是字符串，直接返回格式化的5位数
        return numbers.padStart(5, '0');
    } else if (typeof numbers === 'object') {
        // 如果是对象，尝试提取arrangeNumber或arrange_number字段
        const arrangeNumber = numbers.arrangeNumber || numbers.arrange_number;
        if (arrangeNumber) {
            return arrangeNumber.toString().padStart(5, '0');
        }
    }
    return numbers ? numbers.toString().padStart(5, '0') : '00000';
};

LotteryUI.prototype.getLotteryTypeName = function(lotteryType) {
    switch (lotteryType) {
        case 'double_ball':
            return '双色球';
        case 'super_lotto':
            return '大乐透';
        case 'arrange_five':
            return '排列5';
        default:
            return '未知彩票';
    }
};

// 获取排列5奖励显示
LotteryUI.prototype.getArrangeFiveRewardDisplay = function(item) {
    // 尝试从reward_data中获取物品信息
    if (item.reward_data) {
        try {
            const rewardData = JSON.parse(item.reward_data);
            if (rewardData && rewardData.item && rewardData.amount) {
                // 获取物品显示名称
                const itemNames = {
                    "bread": "面包",
                    "water": "水",
                    "bandage": "绷带",
                    "phone": "手机",
                    "lockpick": "撬锁器",
                    "diamond": "钻石",
                    "gold": "金条",
                    "money": "现金",
                    "car_key": "车钥匙",
                    "weapon_pistol": "手枪",
                    "weapon_knife": "小刀",
                    "weapon_rifle": "步枪",
                    "weapon_carbinerifle": "卡宾枪",
                    "weapon_smg": "冲锋枪"
                };

                const displayName = itemNames[rewardData.item] || rewardData.item;
                if (rewardData.item === "money") {
                    return `<p>奖品: ${displayName} ¥${rewardData.amount}</p>`;
                } else {
                    return `<p>奖品: ${displayName} x${rewardData.amount}</p>`;
                }
            }
        } catch (e) {
            console.warn('解析排列5奖励数据失败:', e);
        }
    }

    // 如果没有reward_data，显示默认信息
    return `<p>奖品: 物品奖励</p>`;
};

// 导出给全局使用
window.lotteryUI = lotteryUI;