# 彩票系统开发文档

## 简介

本系统是一个完整的彩票和刮刮乐系统，包括双色球、大乐透和多种刮刮乐玩法。系统支持奖池管理、员工管理、账户管理等功能。

## 系统命令

### 彩票管理命令

#### 手动开奖命令

```
/lottery_manual_draw [彩票类型] [选项]
```

**参数说明：**
- 彩票类型:
  - `double_ball`: 双色球
  - `super_lotto`: 大乐透
  - `arrange_five`: 排列5
- 选项:
  - `numbers=[号码]`: 指定开奖号码
    - 双色球/大乐透: numbers=1,2,3,4,5,6,7
    - 排列5: numbers=12345
  - `period=[期号]`: 指定期号(如 period=2023001)
  - `date=[日期]`: 指定开奖日期，格式为YYYY-MM-DD(如 date=2023-10-01)

**示例：**
```
/lottery_manual_draw double_ball
/lottery_manual_draw super_lotto numbers=1,2,3,4,5,6,7
/lottery_manual_draw arrange_five numbers=12345
/lottery_manual_draw double_ball period=2023001 numbers=1,2,3,4,5,6,7
```

#### 查询彩票命令

```
/lottery_check_tickets [彩票类型]
```

**参数说明：**
- 彩票类型:
  - `double_ball`: 双色球
  - `super_lotto`: 大乐透
  - `arrange_five`: 排列5

**示例：**
```
/lottery_check_tickets double_ball
/lottery_check_tickets super_lotto
```

### 奖池管理命令

#### 添加奖池金额

```
/lottery_pool add [彩票类型] [金额]
```

**参数说明：**
- 彩票类型:
  - `double_ball`: 双色球
  - `super_lotto`: 大乐透
- 金额: 要添加的金额

**示例：**
```
/lottery_pool add double_ball 1000000
/lottery_pool add super_lotto 500000
```

#### 设置奖池金额

```
/lottery_pool set [彩票类型] [金额]
```

**参数说明：**
- 彩票类型:
  - `double_ball`: 双色球
  - `super_lotto`: 大乐透
- 金额: 要设置的金额

**示例：**
```
/lottery_pool set double_ball 5000000
/lottery_pool set super_lotto 8000000
```

### 刮刮乐命令

#### 使用刮刮乐

```
/usescratch [刮刮乐类型]
```

**参数说明：**
- 刮刮乐类型:
  - `scratch_xixiangfeng`: 喜相逢
  - `scratch_fusong`: 福鼠送彩
  - `scratch_yaocai`: 耀出彩

**示例：**
```
/usescratch scratch_xixiangfeng
/usescratch scratch_fusong
/usescratch scratch_yaocai
```

## 物品系统集成

### 物品配置

在`config.lua`中配置物品系统：

```lua
Config.Items = {
    useScratchItems = true, -- 是否使用刮刮乐物品
    scratchItems = {
        scratch_xixiangfeng = "scratch_card_xixiangfeng",
        scratch_fusong = "scratch_card_fusong", 
        scratch_yaocai = "scratch_card_yaocai"
    }
}
```

### ox_inventory 物品添加

在ox_inventory的`items.lua`文件中添加以下物品：

```lua
['scratch_card_xixiangfeng'] = {
    label = '喜相逢刮刮乐',
    weight = 10,
    stack = true,
    close = true,
    description = '价值100元的喜相逢刮刮乐彩票',
    client = {
        image = 'scratch_card_xixiangfeng.png',
    }
},

['scratch_card_fusong'] = {
    label = '福鼠送彩刮刮乐',
    weight = 10,
    stack = true,
    close = true,
    description = '价值100元的福鼠送彩刮刮乐彩票',
    client = {
        image = 'scratch_card_fusong.png',
    }
},

['scratch_card_yaocai'] = {
    label = '耀出彩刮刮乐',
    weight = 10,
    stack = true,
    close = true,
    description = '价值200元的耀出彩刮刮乐彩票',
    client = {
        image = 'scratch_card_yaocai.png',
    }
}
```

### ESX 物品添加

在ESX服务器的`items.sql`中添加：

```sql
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('scratch_card_xixiangfeng', '喜相逢刮刮乐', 1, 0, 1),
('scratch_card_fusong', '福鼠送彩刮刮乐', 1, 0, 1),
('scratch_card_yaocai', '耀出彩刮刮乐', 1, 0, 1);
```

### QB-Core 物品添加

在QB-Core的`items.lua`中添加：

```lua
["scratch_card_xixiangfeng"] = {
    ["name"] = "scratch_card_xixiangfeng",
    ["label"] = "喜相逢刮刮乐",
    ["weight"] = 100,
    ["type"] = "item",
    ["image"] = "xixiangfeng.png",
    ["unique"] = false,
    ["useable"] = true,
    ["shouldClose"] = true,
    ["combinable"] = nil,
    ["description"] = "价值100元的喜相逢刮刮乐彩票"
},
["scratch_card_fusong"] = {
    ["name"] = "scratch_card_fusong",
    ["label"] = "福鼠送彩刮刮乐",
    ["weight"] = 100,
    ["type"] = "item",
    ["image"] = "fusong.png",
    ["unique"] = false,
    ["useable"] = true,
    ["shouldClose"] = true,
    ["combinable"] = nil,
    ["description"] = "价值100元的福鼠送彩刮刮乐彩票"
},
["scratch_card_yaocai"] = {
    ["name"] = "scratch_card_yaocai",
    ["label"] = "耀出彩刮刮乐",
    ["weight"] = 100,
    ["type"] = "item",
    ["image"] = "yaocai.png",
    ["unique"] = false,
    ["useable"] = true,
    ["shouldClose"] = true,
    ["combinable"] = nil,
    ["description"] = "价值200元的耀出彩刮刮乐彩票"
}
```

### 物品使用方法

系统会自动注册物品使用事件，玩家可以通过以下方式使用刮刮乐物品：

1. 通过物品栏直接使用物品
2. 通过命令使用：`/usescratch [刮刮乐类型]`

## 配置说明

### 主要配置项

系统的主要配置位于`config.lua`文件中，包括以下几个部分：

1. **框架配置**：自动检测使用的框架（ESX/QB）
2. **彩票店配置**：设置彩票店位置、NPC和地图标记
3. **刮刮乐配置**：不同类型刮刮乐的价格、奖金和概率设置
4. **双色球配置**：双色球玩法规则、价格和奖项设置
5. **大乐透配置**：大乐透玩法规则、价格和奖项设置
6. **奖池配置**：初始奖池金额、每注贡献比例和最大滚存期数
7. **资金流向配置**：彩票和刮刮乐收入的分配比例
8. **UI配置**：界面效果和交互设置
9. **货币配置**：货币符号和名称
10. **物品配置**：刮刮乐物品设置
11. **权限配置**：管理员和开奖权限设置
12. **彩票店职业配置**：职业等级和权限设置
13. **通知配置**：中奖公告设置
14. **调试模式**：是否开启调试信息

### 刮刮乐概率设置

刮刮乐的中奖概率可以通过以下配置项调整：

```lua
Config.ScratchSymbols = {
    xiRate = 100,           -- 喜符号出现概率 (%)
    xiXiRate = 2,          -- 囍符号出现概率 (%)
    allSpecialRate = 0,     -- 全部图案都为喜和囍的概率 (%)
    
    -- 福鼠送彩配置
    fusongMatchRates = {
        [0] = 160,   -- 出现0个福鼠号码的概率(权重)
        [1] = 100,   -- 出现1个福鼠号码的概率(权重)
        -- 更多配置...
    },
    
    -- 耀出彩中奖号码数量概率配置
    yaocaiMatchRates = {
        [0] = 160,     -- 出现0个中奖号码的概率(权重)
        [1] = 100,     -- 出现1个中奖号码的概率(权重)
        -- 更多配置...
    },
}
```

### 彩票奖项设置

双色球和大乐透的奖项可以通过以下配置项调整：

```lua
Config.DoubleBall = {
    -- 其他配置...
    prizes = {
        [1] = {match = {6, 1}, amount = 5000000, poolPercent = 0.5, name = "一等奖"},  -- 6红+1蓝，固定500万+奖池50%
        [2] = {match = {6, 0}, amount = 500000, poolPercent = 0.1, name = "二等奖"},   -- 6红，固定50万+奖池10%
        -- 更多奖项...
    }
}

Config.SuperLotto = {
    -- 其他配置...
    prizes = {
        [1] = {match = {5, 2}, amount = 5000000, poolPercent = 0.5, name = "一等奖"}, -- 5前+2后，固定500万+奖池50%
        [2] = {match = {5, 1}, amount = 500000, poolPercent = 0.1, name = "二等奖"},   -- 5前+1后，固定50万+奖池10%
        -- 更多奖项...
    }
}
```

## 系统功能

### 彩票功能

1. **购买彩票**：玩家可以购买双色球和大乐透彩票
2. **自选号码**：玩家可以自选号码或机选号码
3. **定期开奖**：系统会按照设定的时间自动开奖
4. **手动开奖**：管理员可以使用命令手动开奖
5. **奖池管理**：奖池金额会随着彩票销售和中奖情况动态变化
6. **中奖查询**：玩家可以查询自己的中奖情况
7. **奖金兑换**：玩家可以兑换自己的中奖奖金

### 刮刮乐功能

1. **购买刮刮乐**：玩家可以购买不同类型的刮刮乐
2. **刮开卡片**：玩家可以刮开卡片查看是否中奖
3. **即时中奖**：刮刮乐会即时显示中奖结果
4. **奖金兑换**：玩家可以兑换刮刮乐中奖奖金

### 管理系统

1. **账户管理**：管理员可以查看和管理彩票店账户余额
2. **员工管理**：管理员可以管理彩票店员工，包括招聘、解雇、调整薪资等
3. **交易记录**：管理员可以查看所有交易记录
4. **销售统计**：管理员可以查看彩票和刮刮乐的销售统计
5. **中奖记录**：管理员可以查看所有中奖记录
6. **兑奖记录**：管理员可以查看所有兑奖记录
7. **未兑奖记录**：管理员可以查看所有未兑奖记录
8. **手动兑奖**：管理员可以为玩家手动兑奖

## 数据库结构

系统使用以下数据表存储数据：

### lottery_tickets 表



## 注意事项

1. 请确保正确设置权限，避免未授权人员使用管理命令
2. 定期备份数据库，避免数据丢失
3. 调整刮刮乐和彩票的中奖概率时，请确保总体概率合理
4. 系统会自动处理奖池金额，但管理员也可以手动调整
5. 如遇到问题，可以使用`/lottery_fix_tickets`命令修复彩票状态
6. 手续费分成功能会从账户余额中扣除相应金额，请确保账户余额充足 