/* 彩票店UI样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    background: transparent !important;
    color: #fff;
    overflow: hidden;
    user-select: none;
    margin: 0;
    padding: 0;
}

.hidden {
    display: none !important;
}

/* 主容器 */
.lottery-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1200px;
    max-width: 98vw;
    height: 850px;
    max-height: 98vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* 头部 */
.lottery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.lottery-header h2 {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 导航栏 */
.lottery-nav {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-btn {
    flex: 1;
    padding: 15px 20px;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    position: relative;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.nav-btn.active {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
}

.nav-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: #ffd700;
    border-radius: 2px;
}

.nav-btn i {
    margin-right: 8px;
}

/* 内容区域 */
.lottery-content {
    height: calc(100% - 140px);
    overflow-y: auto;
    padding: 30px;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 刮刮乐卡片 */
.scratch-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.card-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.card-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.card-image {
    width: 100%;
    height: 350px;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.card-image img {
    height: 100%;
    width: auto;
    object-fit: contain;
    display: block;
    margin: 0 auto;
    background: transparent;
}

.card-image img.yaocai-img {
    width: 250px;
    max-width: 100%;
    height: 100%;
    object-fit: contain;
}

.card-info h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #ffd700;
}

.price {
    font-size: 20px;
    font-weight: bold;
    color: #ff6b6b;
    margin-bottom: 5px;
}

.max-prize {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
}

/* 按钮样式 */
.buy-btn, .action-btn, .confirm-btn {
    background: linear-gradient(45deg, #ff6b6b, #ffa500);
    border: none;
    color: #fff;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.buy-btn:hover, .action-btn:hover, .confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.action-btn {
    background: linear-gradient(45deg, #4facfe, #00f2fe);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.action-btn:hover {
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.cancel-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.cancel-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 彩票信息 */
.lottery-info {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
}

.lottery-info h3 {
    font-size: 24px;
    color: #ffd700;
    margin-bottom: 10px;
}

.lottery-info p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
}

.prize-pool {
    font-size: 18px;
    font-weight: bold;
}

.pool-amount {
    color: #ff6b6b;
    font-size: 24px;
}

/* 球号选择 */
.ball-selection {
    margin-bottom: 30px;
}

.red-balls-section,
.blue-balls-section,
.front-balls-section,
.back-balls-section {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
}

.ball-selection h4 {
    margin-bottom: 15px;
    color: #ffd700;
    font-size: 16px;
}

.balls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    gap: 8px;
    margin-bottom: 10px;
}

.ball {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    font-size: 14px;
}

.ball:hover {
    transform: scale(1.1);
    border-color: rgba(255, 215, 0, 0.8);
}

.red-balls .ball.selected {
    background: linear-gradient(45deg, #ff4757, #ff3838);
    border-color: #ff3838;
    box-shadow: 0 0 15px rgba(255, 71, 87, 0.5);
}

.blue-balls .ball.selected {
    background: linear-gradient(45deg, #3742fa, #2f3542);
    border-color: #3742fa;
    box-shadow: 0 0 15px rgba(55, 66, 250, 0.5);
}

.front-balls .ball.selected {
    background: linear-gradient(45deg, #ff6348, #ff4757);
    border-color: #ff4757;
    box-shadow: 0 0 15px rgba(255, 99, 72, 0.5);
}

.back-balls .ball.selected {
    background: linear-gradient(45deg, #ffa502, #ff6348);
    border-color: #ffa502;
    box-shadow: 0 0 15px rgba(255, 165, 2, 0.5);
}

.selected-count {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

/* 彩票操作 */
.lottery-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* 历史记录 */
.history-nav {
    display: flex;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    overflow: hidden;
}

.history-nav-btn {
    flex: 1;
    padding: 12px 20px;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.history-nav-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.history-nav-btn.active {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
}

.history-section {
    display: none;
}

.history-section.active {
    display: block;
}

.history-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin-bottom: 5px;
}

.stat-value {
    color: #ffd700;
    font-size: 18px;
    font-weight: bold;
}

.history-list {
    max-height: 500px;
    overflow-y: auto;
}

.history-item {
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 10px;
    border-left: 4px solid #ffd700;
}

.history-item h4 {
    color: #ffd700;
    margin-bottom: 5px;
}

.history-item p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 3px;
}

/* 兑奖中心 */
.claim-info {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
}

.claim-info h3 {
    color: #ffd700;
    font-size: 24px;
    margin-bottom: 10px;
}

.unclaimed-prizes {
    max-height: 600px;
    overflow-y: auto;
}

.prize-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin-bottom: 10px;
    border-left: 4px solid #ff6b6b;
}

.prize-info {
    flex: 1;
}

.prize-info h4 {
    color: #ffd700;
    margin-bottom: 5px;
}

.prize-info p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.prize-amount {
    color: #ff6b6b;
    font-size: 18px;
    font-weight: bold;
    margin-right: 15px;
}

/* 刮刮乐游戏界面 */
.scratch-container {
    background: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    padding: 0 !important;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.8) !important;
}

.scratch-header {
    display: none !important;
}

.scratch-board {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
    width: 100%;
    padding: 0;
    background-color: transparent;
}

.scratch-card {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 720px;
    height: 720px;
    min-width: 720px;
    min-height: 720px;
    max-width: 720px;
    max-height: 720px;
    padding: 0;
    background: transparent;
    border-radius: 15px;
    box-shadow: none;
    margin: 0 auto;
    box-sizing: content-box;
    overflow: hidden;
}

/* 添加银色背景区域 */
.scratch-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: 0;
}

#scratch-canvas, #scratch-areas, .scratch-completed-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}
#scratch-canvas {
    z-index: 3;
}
#scratch-areas {
    z-index: 2;
}
.scratch-completed-image {
    z-index: 0;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}

.scratch-card canvas {
    width: 720px !important;
    height: 720px !important;
    max-width: 100%;
    max-height: 100%;
    display: block;
    margin: auto;
    border-radius: 8px;
    object-fit: cover;
    left: 50%;
}

.scratch-symbol {
    font-size: 16px;
    margin-bottom: 5px;
    color: #ff6b6b;
}

.scratch-amount {
    font-size: 10px;
    color: #333;
}

.scratch-controls {
    padding: 20px 30px;
    display: none;
    justify-content: center;
    gap: 15px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    margin-top: 15px;
    width: auto;
}

.scratch-instructions {
    text-align: center;
    padding: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease-in;
}

.modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 30px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    color: #fff;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.announcement {
    text-align: center;
    min-width: 400px;
}

.announcement-header {
    margin-bottom: 20px;
}

.announcement-header i {
    font-size: 48px;
    color: #ffd700;
    margin-bottom: 10px;
}

.announcement-header h3 {
    font-size: 24px;
    color: #ffd700;
}

.announcement-body {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 25px;
}

.modal-close {
    background: linear-gradient(45deg, #ff6b6b, #ffa500);
    border: none;
    color: #fff;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
}

.confirm {
    min-width: 350px;
}

.confirm-header {
    text-align: center;
    margin-bottom: 20px;
}

.confirm-header i {
    font-size: 32px;
    color: #ffd700;
    margin-bottom: 10px;
}

.confirm-body {
    text-align: center;
    font-size: 16px;
    margin-bottom: 25px;
    line-height: 1.5;
}

.confirm-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* 加载动画 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1001;
    color: #fff;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-left: 4px solid #ffd700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .lottery-container {
        width: 95vw;
        height: 95vh;
    }
    
    .lottery-content {
        padding: 20px;
    }
    
    .scratch-cards {
        grid-template-columns: 1fr;
    }
    
    .balls-grid {
        grid-template-columns: repeat(auto-fill, minmax(35px, 1fr));
    }
    
    .ball {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }
    
    .lottery-actions {
        flex-direction: column;
    }
    
    .scratch-container {
        width: 95vw;
        height: 95vh;
    }
    
    .modal-content {
        margin: 20px;
        min-width: auto;
    }
}

/* 福鼠送彩样式 */
.my-numbers-section {
    grid-column: 1 / -1;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.my-numbers {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 10px;
}

.game-section {
    grid-column: 1 / -1;
}

.game-areas {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.game-area {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

.game-header {
    font-size: 1.1em;
    font-weight: bold;
    margin-bottom: 10px;
    color: #ffd700;
}

.game-numbers {
    display: flex;
    gap: 8px;
    justify-content: center;
    margin-bottom: 10px;
}

.game-prize {
    color: #ffd700;
    font-weight: bold;
}

/* 耀出彩中奖号码显示区，添加定位属性 */
#scratch-areas.yaocai .winning-numbers-container {
    position: absolute;
    top: 35%;  /* 可调整顶部位置 */
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    z-index: 3;
}

/* 耀出彩中奖号码样式 */
.winning-numbers {
    display: flex;
    gap: 50px;
    justify-content: center;
    margin: 10px 0 20px 0;
    font-size: 32px;
    font-weight: bold;
    color: #ffd700;
}

/* 移除中奖号码数字样式，不再使用 */
/* .winning-numbers .number {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
} */

/* 耀出彩号码网格容器，添加定位属性 */
#scratch-areas.yaocai .numbers-grid-container {
    position: absolute;
    top: 47%;  /* 可调整顶部位置 */
    left: 49%;
    transform: translateX(-50%);
    width: 70%; /* 从90%减小到70% */
    height: 70%;
    z-index: 2;
}

/* 修改number-grid样式 */
.number-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    width: 80%; /* 减小整体宽度，使号码更集中 */
    height: 30%;
    box-sizing: border-box;
    padding: 0 10px;
    column-gap: 20px; /* 大幅减小水平间距 */
    row-gap: 2px; /* 增加垂直间距 */
    margin: 0 auto; /* 居中显示 */
}

/* 耀出彩单元格样式，改为类似喜相逢的布局 */
.number-cell {
    height: 60px; /* 固定高度 */
    display: flex;
    flex-direction: column; /* 垂直排列数字和金额 */
    align-items: center;
    justify-content: space-between; /* 均匀分布空间 */
    background: transparent; /* 去掉背景色，改为透明 */
    border-radius: 0; /* 移除圆角 */
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 5px 5px; /* 增加垂直padding */
    position: relative;
    margin-bottom: -5px; /* 去除负margin */
}

/* 耀出彩单元格中的数字样式 */
.number-cell .cell-number {
    font-size: 22px; /* 增大字体大小 */
    color: #ff6b6b;
    font-weight: bold;
    text-align: center;
    height: 25px; /* 固定高度 */
    line-height: 25px; /* 垂直居中文字 */
    margin: 0; /* 移除边距 */
    padding: 0; /* 移除内边距 */
}

/* 耀出彩单元格中的金额样式 */
.number-cell .cell-amount {
    font-size: 15px; /* 略微增大字体大小 */
    color: #333;
    font-weight: bold;
    text-align: center;
    height: 20px; /* 固定高度 */
    line-height: 20px; /* 垂直居中文字 */
    margin: 0; /* 移除边距 */
    padding: 0; /* 移除内边距 */
}

/* 已刮开但未中奖的号码单元格样式 */
.number-cell.scratched .cell-number,
.number-cell.scratched .cell-amount {
    color: rgba(255, 255, 255, 0.5);
}

/* 已刮开且中奖的号码单元格样式 */
.number-cell.winning .cell-number,
.number-cell.winning .cell-amount {
    color: #ffd700;
}

/* 喜相逢：2行5列 */
#scratch-areas.xixiangfeng {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 15px;
    padding: 10px 10px;
    box-sizing: border-box;
    position: absolute;
    
    left: 50.5%;
    transform: translate(-50%, -50%);
    width: 41%;
    height: 21%;
    top: 62.5%;
    z-index: 2;
	column-gap: 50px; /* 调整水平方向的间距 */
    row-gap: 20px;    /* 调整垂直方向的间距 */
    /* 注意：设置了column-gap和row-gap后，gap属性会被覆盖 */
	
}

/* 喜相逢的刮奖区域样式 */
.xixiangfeng .scratch-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1;
    border-radius: 50%;
    background: transparent;
    position: relative;
    cursor: pointer;
    padding: 0;
}

/* 喜相逢的内容区样式 */
.scratch-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 80%;
    left: 20%;
    transform: translate(-50%, -50%);
}

/* 喜相逢的符号样式 */
.xixiangfeng .scratch-symbol {
    font-size: 24px;
    color: #ff6b6b;
    font-weight: bold;
    text-align: center;
    margin-bottom: 5px;
}

/* 喜相逢的金额样式 */
.xixiangfeng .scratch-amount {
    font-size: 16px;
    color: #333;
    font-weight: bold;
    text-align: center;
}

/* 福鼠送彩：新布局样式 */
#scratch-areas.fusong {
    position: relative;
    padding: 20px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 福鼠送彩中奖号码容器 */
.fusong-winning-number-container {
    position: absolute;
    top: 230px;
    right: 155px;
    text-align: center;
    z-index: 3; /* 提高层级确保可见 */
    
    padding: 5px;
    border-radius: 8px;
}

/* 福鼠送彩中奖号码 */
.fusong-winning-number {
    font-size: 30px;
    color: #ff6b6b;
    font-weight: bold;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    background: none;
    border: none;
}

/* 福鼠送彩网格容器 */
.fusong-grid-container {
    position: relative;
    width: 55%; /* 进一步减少宽度 */
    margin: 0 auto;
    z-index: 2;
    top: 285px;
    left: 1%;
    transform: none; /* 移除变换 */
}

/* 福鼠送彩网格表格 */
.fusong-grid {
    width: 100%;
    margin: 0 auto;
    border-collapse: separate;
    border-spacing: 0 17px;
    table-layout: fixed;
}

/* 福鼠送彩网格单元格通用样式 */
.fusong-grid td {
    text-align: center;
    font-weight: bold;
    height: 40px;
    vertical-align: middle;
    background: none;
    border: none;
    padding: 0;
    margin: 0;
}

/* 移除单独处理行间距的样式 */
.fusong-grid tr {
    margin-bottom: 0;
    display: table-row;
}

/* 福鼠送彩数字单元格 */
.fusong-number-cell {
    font-size: 22px;
    color: #ff6b6b;
    cursor: pointer;
    transition: none;
    width: 10%; /* 进一步减小宽度 */
    background: none;
    border-radius: 0;
    padding: 0; /* 减小内边距 */
}

.fusong-number-cell:hover {
    background: none;
    transform: none;
}

/* 福鼠送彩金额单元格 */
.fusong-amount-cell {
    font-size: 18px;
    color: #ffd700;
    font-weight: bold;
    width: 12%; /* 进一步减小宽度 */
    padding: 0; /* 减小内边距 */
}

/* 福鼠送彩已刮开的数字 */
.fusong-number-cell.scratched {
    color: rgba(255, 255, 255, 0.5);
    background: none;
}

/* 福鼠送彩已刮开且中奖的数字 */
.fusong-number-cell.scratched.winning {
    color: #ffd700;
    background: none;
    box-shadow: none;
}

/* 优化整体布局 */
body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background: transparent !important;
    color: #fff;
    overflow: hidden;
    user-select: none;
    margin: 0;
    padding: 0;
}

/* 控制显示区域的样式，消除多余边框 */
#scratch-game {
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden;
}

/* 控制按钮区域样式 */
.scratch-controls {
    padding: 20px 30px;
    display: none;
    justify-content: center;
    gap: 15px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    margin-top: 15px;
    width: auto;
}

/* 耀出彩：5行5列 */
#scratch-areas.yaocai {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 2px;
    padding: 10px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;

    column-gap: 50px; /* 调整水平方向的间距 */
    row-gap: 20px;    /* 调整垂直方向的间距 */
    /* 注意：设置了column-gap和row-gap后，gap属性会被覆盖 */
}

/* ========== 5倍彩钻总体布局容器 ========== */
#scratch-areas.caizuan {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 🔧 主容器2列布局 - 一般不需要改动 */
    grid-template-rows: repeat(10, 1fr);   /* 🔧 主容器10行布局 - 一般不需要改动 */
    gap: 5px; /* 🔧 主容器网格间距 - 一般不需要改动 */
    padding: 10px; /* 🔧 主容器内边距 - 一般不需要改动 */
    width: 100%; /* 🔧 主容器宽度 - 一般不需要改动 */
    height: 100%; /* 🔧 主容器高度 - 一般不需要改动 */
    box-sizing: border-box; /* 🔧 盒模型 - 一般不需要改动 */

    column-gap: 30px; /* 🔧 主容器水平间距 - 一般不需要改动 */
    row-gap: 10px;    /* 🔧 主容器垂直间距 - 一般不需要改动 */

    /* 📝 注意：这个容器主要用于整体布局，具体的位置调整请修改上面的 winning-numbers-container 和 my-numbers-container */
}

/* ========== 5倍彩钻中奖号码整体容器 ========== */
#scratch-areas.caizuan .winning-numbers-container {
    position: absolute;
    top: 52%;  /* 🔧 中奖号码区域距离顶部位置 - 数值越大越靠下 (建议范围: 10%-25%) */
    left: 65%; /* 🔧 水平居中定位 - 一般不需要改动 */
    transform: translateX(-50%); /* 🔧 水平居中偏移 - 一般不需要改动 */
    width: 90%; /* 🔧 中奖号码区域整体宽度 - 数值越大越宽 (建议范围: 80%-100%) */
    z-index: 3; /* 🔧 层级 - 一般不需要改动 */
}

/* ========== 5倍彩钻中奖号码网格布局 (4行5列=20个号码) ========== */
.winning-numbers-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr); /* 🔧 5列布局 - 一般不需要改动 */
    grid-template-rows: repeat(4, 1fr);    /* 🔧 4行布局 - 一般不需要改动 */
    gap: 5px; /* 🔧 所有单元格之间的间距 - 数值越大间距越大 (建议范围: 4px-15px) */
    width: 65%; /* 🔧 网格宽度 - 一般不需要改动 */
    height: auto; /* 🔧 网格高度自适应 - 一般不需要改动 */
    box-sizing: border-box; /* 🔧 盒模型 - 一般不需要改动 */
    padding: 5px; /* 🔧 网格内边距 - 数值越大内容离边框越远 (建议范围: 0px-10px) */
}

/* ========== 5倍彩钻中奖号码单个单元格样式 ========== */
.winning-number-cell {
    height: 50px; /* 🔧 单元格高度 - 数值越大单元格越高 (建议范围: 40px-70px) */
    display: flex; /* 🔧 弹性布局 - 一般不需要改动 */
    flex-direction: column; /* 🔧 垂直排列 - 一般不需要改动 */
    align-items: center; /* 🔧 水平居中 - 一般不需要改动 */
    justify-content: space-between; /* 🔧 垂直分布 - 一般不需要改动 */
    background: transparent; /* 🔧 背景透明 - 一般不需要改动 */
    border-radius: 0; /* 🔧 圆角 - 可以改为5px等数值添加圆角 */
    font-weight: bold; /* 🔧 字体粗细 - 一般不需要改动 */
    cursor: pointer; /* 🔧 鼠标样式 - 一般不需要改动 */
    transition: all 0.3s ease; /* 🔧 动画效果 - 一般不需要改动 */
    padding: 3px; /* 🔧 单元格内边距 - 数值越大内容离边框越远 (建议范围: 2px-8px) */
    position: relative; /* 🔧 定位方式 - 一般不需要改动 */
}

/* ========== 5倍彩钻中奖号码中的符号样式 (数字或💎) ========== */
.winning-number-cell .cell-symbol {
    font-size: 18px; /* 🔧 符号字体大小 - 数值越大符号越大 (建议范围: 14px-24px) */
    color: #ff6b6b; /* 🔧 符号颜色 - 可以改为其他颜色代码 */
    font-weight: bold; /* 🔧 字体粗细 - 一般不需要改动 */
    text-align: center; /* 🔧 文字居中 - 一般不需要改动 */
    height: 20px; /* 🔧 符号区域高度 - 建议与line-height保持一致 */
    line-height: 20px; /* 🔧 行高 - 用于垂直居中，建议与height保持一致 */
    margin: 0; /* 🔧 外边距 - 一般不需要改动 */
    padding: 0; /* 🔧 内边距 - 一般不需要改动 */
}

/* ========== 钻石符号💎特殊样式 ========== */
.winning-number-cell.diamond-symbol .cell-symbol {
    font-size: 20px; /* 🔧 钻石符号大小 - 数值越大钻石越大 (建议范围: 16px-28px) */
    color: #00bfff; /* 🔧 钻石符号颜色 - 蓝色发光效果 */
    text-shadow: 0 0 5px rgba(0, 191, 255, 0.5); /* 🔧 钻石发光效果 - 可调整发光强度 */
}

/* ========== 5倍彩钻中奖号码中的物品名称样式 ========== */
.winning-number-cell .cell-item {
    font-size: 12px; /* 🔧 物品名称字体大小 - 数值越大文字越大 (建议范围: 10px-16px) */
    color: #333; /* 🔧 物品名称颜色 - 可以改为其他颜色代码 */
    font-weight: bold; /* 🔧 字体粗细 - 一般不需要改动 */
    text-align: center; /* 🔧 文字居中 - 一般不需要改动 */
    height: 15px; /* 🔧 物品名称区域高度 - 建议与line-height保持一致 */
    line-height: 15px; /* 🔧 行高 - 用于垂直居中，建议与height保持一致 */
    margin: 0; /* 🔧 外边距 - 一般不需要改动 */
    padding: 0; /* 🔧 内边距 - 一般不需要改动 */
}

/* ========== 5倍彩钻我的号码整体容器 ========== */
#scratch-areas.caizuan .my-numbers-container {
    position: absolute;
    top: 41%;  /* 🔧 我的号码区域距离顶部位置 - 数值越大越靠下 (建议范围: 65%-80%) */
    left: 50%; /* 🔧 水平居中定位 - 一般不需要改动 */
    transform: translateX(-50%); /* 🔧 水平居中偏移 - 一般不需要改动 */
    width: 60%; /* 🔧 我的号码区域整体宽度 - 数值越大越宽 (建议范围: 50%-80%) */
    z-index: 2; /* 🔧 层级 - 一般不需要改动 */
}

/* ========== 5倍彩钻我的号码网格布局 (1行2列=2个号码) ========== */
.my-numbers-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 🔧 2列布局 - 一般不需要改动 */
    column-gap: 2px; /* 🔧 专门控制列间距 */
    row-gap: 3px;     /* 🔧 专门控制行间距 */
    /* gap: 5px; 🔧 注释掉这行 */
    width: 40%; /* 🔧 网格宽度 - 一般不需要改动 */
    height: auto; /* 🔧 网格高度自适应 - 一般不需要改动 */
    box-sizing: border-box; /* 🔧 盒模型 - 一般不需要改动 */
    padding: 5px; /* 🔧 网格内边距 - 数值越大内容离边框越远 (建议范围: 0px-10px) */
}

/* ========== 5倍彩钻我的号码单个单元格样式 ========== */
.my-number-cell {
    height: 60px; /* 🔧 我的号码单元格高度 - 数值越大单元格越高 (建议范围: 50px-80px) */
    display: flex; /* 🔧 弹性布局 - 一般不需要改动 */
    flex-direction: column; /* 🔧 垂直排列 - 一般不需要改动 */
    align-items: center; /* 🔧 水平居中 - 一般不需要改动 */
    justify-content: space-between; /* 🔧 垂直分布 - 一般不需要改动 */
    background: transparent; /* 🔧 背景透明 - 一般不需要改动 */
    border-radius: 0; /* 🔧 圆角 - 可以改为5px等数值添加圆角 */
    font-weight: bold; /* 🔧 字体粗细 - 一般不需要改动 */
    cursor: pointer; /* 🔧 鼠标样式 - 一般不需要改动 */
    transition: all 0.3s ease; /* 🔧 动画效果 - 一般不需要改动 */
    padding: 5px; /* 🔧 单元格内边距 - 数值越大内容离边框越远 (建议范围: 3px-10px) */
    position: relative; /* 🔧 定位方式 - 一般不需要改动 */
}

/* 5倍彩钻我的号码中的数字样式 */
.my-number-cell .cell-number {
    font-size: 24px;
    color: #ff6b6b;
    font-weight: bold;
    text-align: center;
    height: 25px;
    line-height: 25px;
    margin: 0;
    padding: 0;
}

/* ========== 5倍彩钻我的号码中的物品样式 (隐藏显示) ========== */
.my-number-cell .cell-item {
    display: none; /* 🔧 隐藏物品名称显示 - 我的号码下方不显示物品名称 */
    /* 以下为原始样式，如需恢复显示可删除上面的 display: none; */
    font-size: 16px;
    color: #333;
    font-weight: bold;
    text-align: center;
    height: 20px;
    line-height: 20px;
    margin: 0;
    padding: 0;
}

/* 已刮开但未中奖的我的号码单元格样式 */
.my-number-cell.scratched .cell-number,
.my-number-cell.scratched .cell-item {
    color: rgba(255, 255, 255, 0.5);
}

/* 已刮开且中奖的我的号码单元格样式 */
.my-number-cell.winning .cell-number,
.my-number-cell.winning .cell-item {
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
}

/* ========== 中国福总体布局容器 ========== */
#scratch-areas.zhongguofu {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 🔧 主容器2列布局 - 一般不需要改动 */
    grid-template-rows: repeat(8, 1fr);   /* 🔧 主容器8行布局 - 一般不需要改动 */
    gap: 5px; /* 🔧 主容器网格间距 - 一般不需要改动 */
    padding: 10px; /* 🔧 主容器内边距 - 一般不需要改动 */
    width: 100%; /* 🔧 主容器宽度 - 一般不需要改动 */
    height: 100%; /* 🔧 主容器高度 - 一般不需要改动 */
    box-sizing: border-box; /* 🔧 盒模型 - 一般不需要改动 */

    column-gap: 30px; /* 🔧 主容器水平间距 - 一般不需要改动 */
    row-gap: 10px;    /* 🔧 主容器垂直间距 - 一般不需要改动 */

    /* 📝 注意：这个容器主要用于整体布局，具体的位置调整请修改上面的 winning-numbers-container 和 my-numbers-container */
}

/* ========== 中国福中奖号码整体容器 ========== */
#scratch-areas.zhongguofu .winning-numbers-container {
    position: absolute;
    top: 58%;  /* 🔧 中奖号码区域距离顶部位置 - 数值越大越靠下 (建议范围: 10%-25%) */
    left: 66%; /* 🔧 水平居中定位 - 一般不需要改动 */
    transform: translateX(-50%); /* 🔧 水平居中偏移 - 一般不需要改动 */
    width: 90%; /* 🔧 中奖号码区域整体宽度 - 数值越大越宽 (建议范围: 80%-100%) */
    z-index: 3; /* 🔧 层级 - 一般不需要改动 */
}

/* ========== 中国福中奖号码网格布局 (3行5列=15个号码) ========== */
.zhongguofu-winning-numbers-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr); /* 🔧 5列布局 - 一般不需要改动 */
    grid-template-rows: repeat(3, 1fr);    /* 🔧 3行布局 - 一般不需要改动 */
    gap: 5px; /* 🔧 所有单元格之间的间距 - 数值越大间距越大 (建议范围: 4px-15px) */
    width: 65%; /* 🔧 网格宽度 - 一般不需要改动 */
    height: auto; /* 🔧 网格高度自适应 - 一般不需要改动 */
    box-sizing: border-box; /* 🔧 盒模型 - 一般不需要改动 */
    padding: 5px; /* 🔧 网格内边距 - 数值越大内容离边框越远 (建议范围: 0px-10px) */
}

/* ========== 中国福中奖号码单元格样式 ========== */
.zhongguofu-winning-number-cell {
    width: 100%; /* 🔧 中奖号码单元格宽度 - 一般不需要改动 */
    height: 60px; /* 🔧 中奖号码单元格高度 - 数值越大单元格越高 (建议范围: 50px-80px) */
    display: flex; /* 🔧 弹性布局 - 一般不需要改动 */
    flex-direction: column; /* 🔧 垂直排列 - 一般不需要改动 */
    align-items: center; /* 🔧 水平居中 - 一般不需要改动 */
    justify-content: space-between; /* 🔧 垂直分布 - 一般不需要改动 */
    background: transparent; /* 🔧 背景透明 - 一般不需要改动 */
    border-radius: 0; /* 🔧 圆角 - 可以改为5px等数值添加圆角 */
    font-weight: bold; /* 🔧 字体粗细 - 一般不需要改动 */
    cursor: pointer; /* 🔧 鼠标样式 - 一般不需要改动 */
    transition: all 0.3s ease; /* 🔧 动画效果 - 一般不需要改动 */
    padding: 5px; /* 🔧 单元格内边距 - 数值越大内容离边框越远 (建议范围: 3px-10px) */
    position: relative; /* 🔧 定位方式 - 一般不需要改动 */
}

/* 中国福中奖号码中的数字样式 */
.zhongguofu-winning-number-cell .cell-number {
    font-size: 20px;
    color: #4CAF50;
    font-weight: bold;
    text-align: center;
    height: 25px;
    line-height: 25px;
    margin: 0;
    padding: 0;
}

/* 中国福中奖号码中的福符号样式 */
.zhongguofu-winning-number-cell .cell-symbol {
    font-size: 20px;
    color: #4CAF50; /* 🔧 福符号颜色与数字颜色一致 */
    font-weight: bold;
    text-align: center;
    height: 25px;
    line-height: 25px;
    margin: 0;
    padding: 0;
}

/* 中国福中奖号码中的物品样式 */
.zhongguofu-winning-number-cell .cell-item {
    font-size: 12px;
    color: #333;
    font-weight: bold;
    text-align: center;
    height: 20px;
    line-height: 20px;
    margin: 0;
    padding: 0;
}

/* ========== 中国福我的号码整体容器 ========== */
#scratch-areas.zhongguofu .my-numbers-container {
    position: absolute;
    top: 46%;  /* 🔧 我的号码区域距离顶部位置 - 数值越大越靠下 (建议范围: 65%-80%) */
    left: 65%; /* 🔧 水平居中定位 - 一般不需要改动 */
    transform: translateX(-50%); /* 🔧 水平居中偏移 - 一般不需要改动 */
    width: 60%; /* 🔧 我的号码区域整体宽度 - 数值越大越宽 (建议范围: 50%-80%) */
    z-index: 2; /* 🔧 层级 - 一般不需要改动 */
}

/* ========== 中国福我的号码网格布局 (1行2列=2个号码) ========== */
.zhongguofu-my-numbers-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 🔧 2列布局 - 一般不需要改动 */
    column-gap: 2px; /* 🔧 专门控制列间距 */
    row-gap: 3px;     /* 🔧 专门控制行间距 */
    width: 50%; /* 🔧 网格宽度 - 一般不需要改动 */
    height: auto; /* 🔧 网格高度自适应 - 一般不需要改动 */
    box-sizing: border-box; /* 🔧 盒模型 - 一般不需要改动 */
    padding: 5px; /* 🔧 网格内边距 - 数值越大内容离边框越远 (建议范围: 0px-10px) */
}

/* ========== 中国福我的号码单元格样式 ========== */
.zhongguofu-my-number-cell {
    width: 100%; /* 🔧 我的号码单元格宽度 - 一般不需要改动 */
    height: 60px; /* 🔧 我的号码单元格高度 - 数值越大单元格越高 (建议范围: 50px-80px) */
    display: flex; /* 🔧 弹性布局 - 一般不需要改动 */
    flex-direction: column; /* 🔧 垂直排列 - 一般不需要改动 */
    align-items: center; /* 🔧 水平居中 - 一般不需要改动 */
    justify-content: space-between; /* 🔧 垂直分布 - 一般不需要改动 */
    background: transparent; /* 🔧 背景透明 - 一般不需要改动 */
    border-radius: 0; /* 🔧 圆角 - 可以改为5px等数值添加圆角 */
    font-weight: bold; /* 🔧 字体粗细 - 一般不需要改动 */
    cursor: pointer; /* 🔧 鼠标样式 - 一般不需要改动 */
    transition: all 0.3s ease; /* 🔧 动画效果 - 一般不需要改动 */
    padding: 5px; /* 🔧 单元格内边距 - 数值越大内容离边框越远 (建议范围: 3px-10px) */
    position: relative; /* 🔧 定位方式 - 一般不需要改动 */
}

/* 中国福我的号码中的数字样式 */
.zhongguofu-my-number-cell .cell-number {
    font-size: 24px;
    color: #ff6b6b;
    font-weight: bold;
    text-align: center;
    height: 25px;
    line-height: 25px;
    margin: 0;
    padding: 0;
}

/* ========== 中国福我的号码中的物品样式 (隐藏显示) ========== */
.zhongguofu-my-number-cell .cell-item {
    display: none; /* 🔧 隐藏物品名称显示 - 我的号码下方不显示物品名称 */
    font-size: 16px;
    color: #333;
    font-weight: bold;
    text-align: center;
    height: 20px;
    line-height: 20px;
    margin: 0;
    padding: 0;
}

/* 已刮开但未中奖的中国福我的号码单元格样式 */
.zhongguofu-my-number-cell.scratched .cell-number,
.zhongguofu-my-number-cell.scratched .cell-item {
    color: rgba(255, 255, 255, 0.5);
}

/* 已刮开且中奖的中国福我的号码单元格样式 */
.zhongguofu-my-number-cell.winning .cell-number,
.zhongguofu-my-number-cell.winning .cell-item {
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
}

/* ========== 乘风破浪总体布局容器 ========== */
#scratch-areas.chengfeng {
    display: grid;
    grid-template-columns: repeat(5, 1fr); /* 🔧 主容器5列布局 - 一般不需要改动 */
    grid-template-rows: repeat(4, 1fr);   /* 🔧 主容器4行布局 - 一般不需要改动 */
    gap: 5px; /* 🔧 主容器网格间距 - 一般不需要改动 */
    padding: 10px; /* 🔧 主容器内边距 - 一般不需要改动 */
    width: 100%; /* 🔧 主容器宽度 - 一般不需要改动 */
    height: 100%; /* 🔧 主容器高度 - 一般不需要改动 */
    box-sizing: border-box; /* 🔧 盒模型 - 一般不需要改动 */
}

/* ========== 乘风破浪图标整体容器 ========== */
#scratch-areas.chengfeng .chengfeng-icons-grid-container {
    position: absolute;
    top: 83%;  /* 🔧 图标区域距离顶部位置 - 数值越大越靠下 */
    left: 69%; /* 🔧 水平居中定位 - 一般不需要改动 */
    transform: translate(-50%, -50%); /* 🔧 居中偏移 - 一般不需要改动 */
    width: 90%; /* 🔧 图标区域整体宽度 - 数值越大越宽 */
    height: 80%; /* 🔧 图标区域整体高度 - 数值越大越高 */
    z-index: 3; /* 🔧 层级 - 一般不需要改动 */
}

/* ========== 乘风破浪图标网格布局 (4行5列=20个图标) ========== */
.chengfeng-icons-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr); /* 🔧 5列布局 - 一般不需要改动 */
    grid-template-rows: repeat(4, 1fr);    /* 🔧 4行布局 - 一般不需要改动 */
    gap: px; /* 🔧 所有单元格之间的间距 - 数值越大间距越大 */
    width: 60%; /* 🔧 网格宽度 - 一般不需要改动 */
    height: 53%; /* 🔧 网格高度 - 一般不需要改动 */
    box-sizing: border-box; /* 🔧 盒模型 - 一般不需要改动 */
    padding: 10px; /* 🔧 网格内边距 - 数值越大内容离边框越远 */
}

/* ========== 乘风破浪图标单元格基础样式 ========== */
.chengfeng-icon-cell {
    width: 100%; /* 🔧 图标单元格宽度 - 一般不需要改动 */
    height: 100%; /* 🔧 图标单元格高度 - 一般不需要改动 */
    display: flex; /* 🔧 弹性布局 - 一般不需要改动 */
    flex-direction: column; /* 🔧 垂直排列 - 一般不需要改动 */
    align-items: center; /* 🔧 水平居中 - 一般不需要改动 */
    justify-content: center; /* 🔧 垂直居中 - 一般不需要改动 */
    font-weight: bold; /* 🔧 字体粗细 - 一般不需要改动 */
    position: relative; /* 🔧 定位方式 - 一般不需要改动 */
    /* 移除背景、边框、圆角等装饰样式，只显示内容 */
}

/* 乘风破浪图标单元格悬停效果 - 已移除，只显示内容 */

/* ========== 乘风破浪图标样式 ========== */
.chengfeng-icon-cell .cell-icon {
    font-size: 24px; /* 🔧 图标大小 - 调小一些 */
    text-align: center;
    height: 30px;
    line-height: 30px;
    margin: 0;
    padding: 0;
    margin-bottom: 5px; /* 🔧 图标与物品名称间距 */
}

/* ⛵ 帆船图标特殊样式 (1倍奖励) - 移除背景和边框 */

.chengfeng-icon-cell.sail-icon .cell-icon {
    color: #4CAF50; /* 🔧 帆船图标颜色 - 绿色表示1倍奖励 */
    text-shadow: 0 0 8px rgba(76, 175, 80, 0.8);
}

/* 🌪 龙卷风图标特殊样式 (2倍奖励) - 移除背景和边框 */

.chengfeng-icon-cell.tornado-icon .cell-icon {
    color: #FFC107; /* 🔧 龙卷风图标颜色 - 金色表示2倍奖励 */
    text-shadow: 0 0 8px rgba(255, 193, 7, 0.8);
}

/* 🌊 普通图标样式 - 移除背景和边框，支持多样化图标 */

.chengfeng-icon-cell.normal-icon .cell-icon {
    color: #87CEEB; /* 🔧 普通图标颜色 - 天蓝色表示普通 */
    text-shadow: 0 0 6px rgba(135, 206, 235, 0.6);
}

/* ========== 乘风破浪物品名称样式 ========== */
.chengfeng-icon-cell .cell-item {
    font-size: 12px; /* 🔧 物品名称字体大小 */
    color: #fff; /* 🔧 物品名称颜色 */
    font-weight: bold;
    text-align: center;
    height: 16px;
    line-height: 16px;
    margin: 0;
    padding: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8); /* 🔧 文字阴影增强可读性 */
}

/* Toast 消息 */
.toast-container {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    width: 100%;
    max-width: 400px;
    pointer-events: none; /* 允许点击穿透 */
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 250px;
    max-width: 350px;
    background-color: rgba(0, 0, 0, 0.85);
    color: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.toast.show {
    transform: translateX(0);
}

.toast-title {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 14px;
}

.toast-message {
    font-size: 13px;
}

.toast-success {
    border-left: 4px solid #4CAF50;
}

.toast-error {
    border-left: 4px solid #F44336;
}

.toast-info {
    border-left: 4px solid #2196F3;
}

.toast-warning {
    border-left: 4px solid #FF9800;
}

/* 管理系统按钮 */
.admin-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 10px;
}

.admin-btn:hover {
    transform: scale(1.1);
}

/* 添加中奖和未中奖状态的样式 */
.winning-status {
    color: #2ecc71;
    font-weight: bold;
}

.losing-status {
    color: #e74c3c;
    font-weight: bold;
}

/* 历史记录中的号码球样式 */
.history-ball {
    display: inline-block;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    text-align: center;
    line-height: 25px;
    font-size: 12px;
    font-weight: bold;
    margin: 0 2px;
    color: white;
}

.red-ball {
    background: linear-gradient(135deg, #ff4757, #ff3838);
    box-shadow: 0 2px 5px rgba(255, 71, 87, 0.5);
}

.blue-ball {
    background: linear-gradient(135deg, #3742fa, #1e90ff);
    box-shadow: 0 2px 5px rgba(55, 66, 250, 0.5);
}

.front-ball {
    background: linear-gradient(135deg, #ff6348, #ff4757);
    box-shadow: 0 2px 5px rgba(255, 99, 72, 0.5);
}

.back-ball {
    background: linear-gradient(135deg, #ffa502, #ff6348);
    box-shadow: 0 2px 5px rgba(255, 165, 2, 0.5);
}

/* 分隔符样式 */
.ball-separator {
    display: inline-block;
    margin: 0 5px;
    font-weight: bold;
    color: #ffd700;
}

/* 管理员通知样式 */
.admin-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 250px;
    max-width: 350px;
    background-color: rgba(0, 0, 0, 0.85);
    color: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.admin-toast.show {
    transform: translateX(0);
}

.admin-toast-title {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 14px;
}

.admin-toast-message {
    font-size: 13px;
}

.admin-toast-success {
    border-left: 4px solid #4CAF50;
}

.admin-toast-error {
    border-left: 4px solid #F44336;
}

.admin-toast-info {
    border-left: 4px solid #2196F3;
}

.admin-toast-warning {
    border-left: 4px solid #FF9800;
}

/* 手续费信息样式 */
.fee-info {
    padding: 10px;
    text-align: center;
}

.fee-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #4CAF50;
}

.fee-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    padding: 15px;
    border-radius: 5px;
    margin-top: 10px;
    text-align: left;
}

.fee-details p {
    margin: 0;
    font-size: 16px;
    color: #eee;
}

.fee-details .highlight {
    color: #FFC107;
    font-weight: bold;
    font-size: 18px;
}

/* 排列5样式 */
.arrange-five-selection {
    margin-bottom: 30px;
}

.number-input-section {
    margin-bottom: 30px;
    text-align: center;
}

.number-input-section h4 {
    color: #ffd700;
    margin-bottom: 20px;
    font-size: 18px;
}

.number-input-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.arrange-five-input {
    width: 300px;
    height: 50px;
    font-size: 24px;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    letter-spacing: 8px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.arrange-five-input:focus {
    outline: none;
    border-color: #ffd700;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.arrange-five-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
    letter-spacing: normal;
    font-size: 16px;
}

.input-hint {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.input-hint i {
    color: #4facfe;
}

.selected-number-display {
    text-align: center;
    margin-bottom: 20px;
}

.selected-number-display h4 {
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 16px;
}

.selected-arrange-number {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 20px 0;
}

.number-digit {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    color: #fff;
    font-size: 24px;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.number-digit.highlight {
    border-color: #ffd700;
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    color: #333;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}