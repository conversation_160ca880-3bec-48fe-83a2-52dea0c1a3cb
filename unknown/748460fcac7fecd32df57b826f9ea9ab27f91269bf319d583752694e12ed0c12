-- 彩票系统缓存模块

-- 调试打印函数 - 只在调试模式开启时打印
local function DebugPrint(message, level)
    if Config.Debug and Config.Debug.enabled then
        level = level or "^7" -- 默认白色
        print(level .. "[彩票系统缓存-调试] " .. message)
    end
end

-- 检查缓存配置
local cacheEnabled = Config.Cache and Config.Cache.enabled
if cacheEnabled == nil then
    cacheEnabled = true -- 默认启用缓存
end

-- 缓存系统
Cache = {
    playerTickets = {}, -- 玩家彩票记录缓存
    drawHistory = {},   -- 开奖历史缓存
    prizePools = {},    -- 奖池数据缓存
    unclaimedPrizes = {}, -- 未兑奖奖品缓存
    cacheTTL = Config.Cache and Config.Cache.ttl or 30,      -- 缓存有效期(秒)
    queryTimes = {},    -- 查询时间统计
    enabled = cacheEnabled -- 是否启用缓存
}

-- 获取缓存
function Cache:Get(cacheType, key)
    -- 如果缓存被禁用，直接返回nil
    if not self.enabled then
        return nil
    end
    
    if not self[cacheType] or not self[cacheType][key] then
        return nil
    end
    
    -- 检查缓存是否过期
    if os.time() - self[cacheType][key].timestamp > self.cacheTTL then
        DebugPrint("^3缓存过期: " .. cacheType .. " - " .. key)
        self[cacheType][key] = nil
        return nil
    end
    
    DebugPrint("^2使用缓存: " .. cacheType .. " - " .. key)
    return self[cacheType][key].data
end

-- 设置缓存
function Cache:Set(cacheType, key, data)
    -- 如果缓存被禁用，直接返回
    if not self.enabled then
        return
    end
    
    if not self[cacheType] then
        self[cacheType] = {}
    end

    -- 确保缓存在设置前数据格式正确
    if cacheType == "playerTickets" and data and type(data) == "table" then
        -- 检查是否需要处理刮刮乐数据
        for i, record in ipairs(data) do
            if record.card_type and record.total_prize ~= nil then
                -- 确保total_prize是数字类型
                if type(record.total_prize) == "string" then
                    record.total_prize = tonumber(record.total_prize) or 0
                end
            end
        end
    end
    
    self[cacheType][key] = {
        data = data,
        timestamp = os.time()
    }
    DebugPrint("^2缓存设置: " .. cacheType .. " - " .. key)
end

-- 清除特定缓存
function Cache:Clear(cacheType, key)
    if not self.enabled then
        return
    end
    
    if self[cacheType] and self[cacheType][key] then
        self[cacheType][key] = nil
        DebugPrint("^3缓存清除: " .. cacheType .. " - " .. key)
        return true
    end
    
    return false
end

-- 清除玩家的所有缓存
function Cache:ClearPlayerCache(playerId)
    if not self.enabled or not playerId then return end
    
    for cacheType, items in pairs(self) do
        if type(items) == "table" then
            for key, _ in pairs(items) do
                if string.find(key, playerId) then
                    self[cacheType][key] = nil
                    DebugPrint("^3清除玩家缓存: " .. playerId .. " - " .. cacheType .. " - " .. key)
                end
            end
        end
    end
end

-- 记录查询时间
function Cache:RecordQueryTime(queryType, startTime)
    local endTime = os.clock()
    local duration = (endTime - startTime) * 1000 -- 转换为毫秒
    
    if not self.queryTimes[queryType] then
        self.queryTimes[queryType] = {
            count = 0,
            totalTime = 0,
            maxTime = 0,
            lastTime = duration
        }
    end
    
    self.queryTimes[queryType].count = self.queryTimes[queryType].count + 1
    self.queryTimes[queryType].totalTime = self.queryTimes[queryType].totalTime + duration
    self.queryTimes[queryType].lastTime = duration
    
    if duration > self.queryTimes[queryType].maxTime then
        self.queryTimes[queryType].maxTime = duration
    end
    
    if duration > 1000 then -- 如果查询超过1秒就记录
        DebugPrint("^1查询耗时较长: " .. queryType .. " - " .. string.format("%.2f", duration) .. " ms")
    end
    
    return duration
end

-- 打印缓存统计信息
function Cache:PrintStats()
    local stats = {
        totalItems = 0,
        byType = {}
    }
    
    for cacheType, items in pairs(self) do
        if type(items) == "table" and cacheType ~= "queryTimes" then
            local count = 0
            for _ in pairs(items) do
                count = count + 1
            end
            
            stats.byType[cacheType] = count
            stats.totalItems = stats.totalItems + count
        end
    end
    
    DebugPrint("^6缓存统计: 共 " .. stats.totalItems .. " 条缓存")
    for cacheType, count in pairs(stats.byType) do
        DebugPrint("^6  - " .. cacheType .. ": " .. count .. " 条")
    end
    
    -- 打印查询时间统计
    DebugPrint("^6查询时间统计:")
    for queryType, timeStats in pairs(self.queryTimes) do
        local avgTime = timeStats.count > 0 and timeStats.totalTime / timeStats.count or 0
        DebugPrint(string.format("^6  - %s: 查询次数=%d, 平均=%.2f ms, 最长=%.2f ms, 最近=%.2f ms", 
            queryType, timeStats.count, avgTime, timeStats.maxTime, timeStats.lastTime))
    end
end

-- 仅当缓存启用时创建自动清理线程
if Cache.enabled then
    Citizen.CreateThread(function()
        DebugPrint("^2缓存系统已启用，TTL=" .. Cache.cacheTTL .. "秒")
        
        while true do
            Citizen.Wait(60000) -- 每分钟检查一次
            
            local now = os.time()
            local cleared = 0
            
            for cacheType, cache in pairs(Cache) do
                if type(cache) == "table" and cacheType ~= "queryTimes" then
                    for key, item in pairs(cache) do
                        if now - item.timestamp > Cache.cacheTTL then
                            cache[key] = nil
                            cleared = cleared + 1
                        end
                    end
                end
            end
            
            if cleared > 0 then
                DebugPrint("^3自动清理过期缓存: " .. cleared .. " 条")
            end
            
            -- 每x分钟打印一次统计信息(从配置读取)
            local statsInterval = Config.Cache and Config.Cache.statsInterval or 10
            if (now % (statsInterval * 60)) < 60 then
                Cache:PrintStats()
            end
        end
    end)
else
    DebugPrint("^1缓存系统已禁用")
end

-- 导出模块
return Cache 