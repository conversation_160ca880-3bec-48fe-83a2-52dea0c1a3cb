-- 检查 lottery_offline_prizes 表结构的脚本
-- 可以在服务器控制台运行这个函数来检查表结构

function CheckOfflinePrizesTableStructure()
    print("^3[彩票系统] ^7开始检查 lottery_offline_prizes 表结构...")
    
    MySQL.Async.fetchAll([[
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'lottery_offline_prizes'
        ORDER BY ORDINAL_POSITION
    ]], {}, function(result)
        if result and #result > 0 then
            print("^2[彩票系统] ^7lottery_offline_prizes 表结构:")
            print("^7字段名称 | 数据类型 | 可空 | 默认值")
            print("^7" .. string.rep("-", 50))
            
            local hasPlayerName = false
            local hasTicketId = false
            local hasClaimed = false
            local hasIsClaimed = false
            
            for _, column in ipairs(result) do
                local nullable = column.IS_NULLABLE == "YES" and "是" or "否"
                local defaultValue = column.COLUMN_DEFAULT or "无"
                print(string.format("^7%s | %s | %s | %s", 
                    column.COLUMN_NAME, 
                    column.DATA_TYPE, 
                    nullable, 
                    defaultValue
                ))
                
                -- 检查关键字段
                if column.COLUMN_NAME == "player_name" then
                    hasPlayerName = true
                elseif column.COLUMN_NAME == "ticket_id" then
                    hasTicketId = true
                elseif column.COLUMN_NAME == "claimed" then
                    hasClaimed = true
                elseif column.COLUMN_NAME == "is_claimed" then
                    hasIsClaimed = true
                end
            end
            
            print("^7" .. string.rep("-", 50))
            print("^3[彩票系统] ^7字段检查结果:")
            print("^7player_name: " .. (hasPlayerName and "^2存在" or "^1缺失"))
            print("^7ticket_id: " .. (hasTicketId and "^2存在" or "^1缺失"))
            print("^7claimed: " .. (hasClaimed and "^2存在" or "^1缺失"))
            print("^7is_claimed: " .. (hasIsClaimed and "^3存在(旧字段)" or "^7不存在"))
            
            if hasPlayerName and hasTicketId and (hasClaimed or hasIsClaimed) then
                print("^2[彩票系统] ^7表结构检查通过！")
            else
                print("^1[彩票系统] ^7表结构不完整，需要修复！")
            end
        else
            print("^1[彩票系统] ^7lottery_offline_prizes 表不存在！")
        end
    end)
end

-- 修复表结构的函数
function FixOfflinePrizesTableStructure()
    print("^3[彩票系统] ^7开始修复 lottery_offline_prizes 表结构...")
    
    -- 确保表存在
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_offline_prizes` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `player_id` varchar(50) NOT NULL,
            `player_name` varchar(50) NOT NULL,
            `prize_amount` int(11) NOT NULL,
            `prize_type` varchar(50) NOT NULL,
            `ticket_id` int(11) NOT NULL,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            `claimed` tinyint(1) DEFAULT 0,
            PRIMARY KEY (`id`),
            KEY `idx_player_claimed` (`player_id`, `claimed`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {}, function()
        print("^2[彩票系统] ^7表创建/检查完成")
        
        -- 添加缺失的字段
        MySQL.Async.execute([[
            ALTER TABLE lottery_offline_prizes 
            ADD COLUMN IF NOT EXISTS player_name VARCHAR(50) NOT NULL AFTER player_id
        ]], {})
        
        MySQL.Async.execute([[
            ALTER TABLE lottery_offline_prizes 
            ADD COLUMN IF NOT EXISTS ticket_id INT NOT NULL AFTER prize_type
        ]], {})
        
        MySQL.Async.execute([[
            ALTER TABLE lottery_offline_prizes 
            ADD COLUMN IF NOT EXISTS claimed TINYINT(1) DEFAULT 0
        ]], {})
        
        print("^2[彩票系统] ^7表结构修复完成！")
        
        -- 重新检查表结构
        Citizen.SetTimeout(1000, function()
            CheckOfflinePrizesTableStructure()
        end)
    end)
end

-- 导出函数供控制台使用
_G.CheckOfflinePrizesTableStructure = CheckOfflinePrizesTableStructure
_G.FixOfflinePrizesTableStructure = FixOfflinePrizesTableStructure

print("^2[彩票系统] ^7表结构检查工具已加载")
print("^7使用方法:")
print("^7CheckOfflinePrizesTableStructure() - 检查表结构")
print("^7FixOfflinePrizesTableStructure() - 修复表结构")
